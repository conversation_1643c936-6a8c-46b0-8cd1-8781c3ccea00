'use client'

import Filter from '@/components/filter'
import { useLazyQuery } from '@apollo/client'
import { useEffect, useState } from 'react'
import { List, TableSkeleton } from '../../../components/skeletons'
import Link from 'next/link'
import { GET_KEY_CONTACTS } from '@/app/lib/graphQL/query'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { H3 } from '@/components/ui/typography'
import { Button } from '@/components/ui/button'
import { Check, PlusCircle, Users } from 'lucide-react'
import TableWrapper from '@/components/ui/table-wrapper'
import {
    Card,
    CardContent,
    CardHeader,
    ListHeader,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui'

export interface IKeyContactPermission {
    EDIT_KEY_CONTACT?: boolean
    DELETE_KEY_CONTACT?: boolean
}

export default function KeyContactList() {
    const [keyContacts, setKeyContacts] = useState([] as any)
    const [groupByCompany, setGroupByCompany] = useState(false)
    const [permission, setPermission] = useState<IKeyContactPermission>({
        EDIT_KEY_CONTACT: undefined,
        DELETE_KEY_CONTACT: undefined,
    })

    const handleFilterChange = ({
        type,
        data,
    }: {
        type: string
        data: any
    }) => {
        let searchKeywordFilters: any[] = []
        if (type === 'keyword' && data.value !== '') {
            const fieldsToFilter = ['firstName', 'surname', 'phone', 'email']

            searchKeywordFilters = fieldsToFilter.map(function (field) {
                return {
                    [field]: { contains: data.value },
                }
            })
        }

        loadData(searchKeywordFilters)
    }

    const [queryGetKeyContacts, { called, loading }] = useLazyQuery(
        GET_KEY_CONTACTS,
        {
            fetchPolicy: 'cache-and-network',
            // onCompleted: (response: any) => {
            //     const data = response.readSuppliers.nodes
            //     if (data) {
            //         setSuppliers(data)
            //     }
            // },
            onError: (error: any) => {
                console.error('querySupplier error', error)
            },
        },
    )

    const loadData = async (searchKeywordFilters: any[] = []) => {
        if (searchKeywordFilters.length > 0) {
            const promises = searchKeywordFilters.map(
                async (keywordFilter: any) => {
                    return await queryGetKeyContacts({
                        variables: {
                            filter: keywordFilter,
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) => r.data.readKeyContacts.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readKeyContacts.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            setKeyContacts(responses)
        } else {
            const { data } = await queryGetKeyContacts()
            setKeyContacts(data?.readKeyContacts.nodes ?? [])
        }
    }

    const initPermission = () => {
        const permissions = getPermissions('EDIT_KEY_CONTACT')

        Object.keys(permission).forEach((value) => {
            const hasThisPermission = hasPermission(value, permissions)

            setPermission((prev) => ({ ...prev, [value]: hasThisPermission }))
        })
    }

    useEffect(() => {
        loadData()
        initPermission()
    }, [])

    return (
        <>
            <ListHeader
                title="Key Contacts"
                actions={
                    <div className="flex items-center gap-2.5">
                        <Button
                            variant={
                                groupByCompany ? 'primary' : 'primaryOutline'
                            }
                            onClick={() => setGroupByCompany((prev) => !prev)}
                            iconLeft={Users}>
                            Group by Company
                        </Button>
                        {permission.EDIT_KEY_CONTACT && (
                            <Link href="/key-contacts/create">
                                <Button iconLeft={PlusCircle}>
                                    New Key Contact
                                </Button>
                            </Link>
                        )}
                    </div>
                }
            />
            <Card className="mt-8">
                <CardHeader>
                    <Filter onChange={handleFilterChange} />
                </CardHeader>
                <CardContent>
                    {called && loading ? (
                        <TableSkeleton />
                    ) : (
                        <>
                            {keyContacts.length == 0 ? (
                                <div className="text-center font-semibold w-full flex items-center justify-center h-20">
                                    No Data Found
                                </div>
                            ) : (
                                <DataPresentation
                                    data={keyContacts}
                                    groupByCompany={groupByCompany}
                                />
                            )}
                        </>
                    )}
                </CardContent>
            </Card>
        </>
    )
}

const DataPresentation = ({
    data,
    groupByCompany,
}: {
    data: any
    groupByCompany: boolean
}) => {
    if (!groupByCompany) {
        return (
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Key Contacts</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Phone</TableHead>
                        <TableHead>Address</TableHead>
                        <TableHead>Company</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {(data as any[]).map((keyContact: any, index: number) => (
                        <TableRow key={index}>
                            <TableCell>
                                <Link
                                    href={`/key-contacts/edit?id=${keyContact.id}`}>
                                    {keyContact.firstName} {keyContact.surname}
                                </Link>
                            </TableCell>
                            <TableCell>{keyContact.email ?? '-'}</TableCell>
                            <TableCell>
                                <div className="mb-1">
                                    Phone: {keyContact.phone ?? '-'}
                                </div>
                                <div>
                                    Cell Phone: {keyContact.cellPhone ?? '-'}
                                </div>
                            </TableCell>
                            <TableCell>{keyContact.address ?? '-'}</TableCell>
                            <TableCell>
                                {keyContact.company?.title ?? '-'}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        )
    }

    const groupedData = data.reduce((prev: any, current: any) => {
        const companyID = `${current.companyID ?? 0}`

        if (Object.hasOwn(prev, companyID)) {
            return prev
        }

        const companyKeyContacts = data.filter(
            (item: any) => item.companyID === current.companyID,
        )

        return {
            ...prev,
            [companyID]: companyKeyContacts,
        }
    }, {})

    return (
        <div className="w-full flex flex-col gap-4">
            {Object.entries(groupedData).map(function (grouped) {
                const [_, keyContacts] = grouped as any

                const companyName = keyContacts[0].company.title ?? 'No Company'
                return (
                    <div>
                        <div className="border border-b-0 font-bold border-secondary pl-5 max-w-sm rounded-t-lg py-2 px-3">
                            {companyName}
                        </div>
                        <div className="border rounded-tl-none rounded-lg border-secondary pt-3">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Key Contacts</TableHead>
                                        <TableHead>Email</TableHead>
                                        <TableHead>Phone</TableHead>
                                        <TableHead>Address</TableHead>
                                        <TableHead>Company</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {(data as any[]).map(
                                        (keyContact: any, index: number) => (
                                            <TableRow key={index}>
                                                <TableCell>
                                                    <Link
                                                        href={`/key-contacts/edit?id=${keyContact.id}`}>
                                                        {keyContact.firstName}{' '}
                                                        {keyContact.surname}
                                                    </Link>
                                                </TableCell>
                                                <TableCell>
                                                    {keyContact.email ?? '-'}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="mb-1">
                                                        Phone:{' '}
                                                        {keyContact.phone ??
                                                            '-'}
                                                    </div>
                                                    <div>
                                                        Cell Phone:{' '}
                                                        {keyContact.cellPhone ??
                                                            '-'}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {keyContact.address ?? '-'}
                                                </TableCell>
                                                <TableCell>
                                                    {keyContact.company
                                                        ?.title ?? '-'}
                                                </TableCell>
                                            </TableRow>
                                        ),
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                )
            })}
        </div>
    )
}
