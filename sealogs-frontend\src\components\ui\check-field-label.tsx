'use client'

import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroupItem } from '@/components/ui/radio-group'
import { cn } from '@/app/lib/utils'
import { P } from './typography'

const checkFieldLabelVariants = cva(
    'cursor-pointer w-full px-4 rounded-e-lg will-change-transform will-change-color will-change-padding transform-gpu hover:transition-all hover:ease-out hover:duration-300',
    {
        variants: {
            variant: {
                default: 'hover:bg-fire-bush-100 hover:border-yellow-vivid-600',
                primary:
                    'hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600',
                secondary: 'hover:bg-background hover:border-neutral-400',
                success: 'hover:bg-bright-turquoise-100 hover:border-teal-600',
                destructive: 'hover:bg-red-vivid-50 hover:border-red-vivid-600',
                warning: 'hover:bg-fire-bush-100 hover:border-yellow-vivid-600',
                pink: 'hover:bg-pink-vivid-50 hover:border-pink-vivid-600',
                outline: 'hover:bg-background hover:border-neutral-400',
                'light-blue':
                    'hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600',
            },
            size: {
                default: 'py-[10.5px]',
                sm: 'py-2',
                lg: 'py-6',
            },
            disabled: {
                true: 'hover:bg-transparent hover:border-border',
                false: '',
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
            disabled: false,
        },
    },
)

const innerWrapperVariants = cva(
    'relative inset-x-0 w-12 rounded-s-lg justify-center flex items-center',
    {
        variants: {
            variant: {
                default: 'bg-light-blue-vivid-50 border-light-blue-vivid-600',
                primary: 'bg-light-blue-vivid-50 border-light-blue-vivid-600',
                secondary: 'bg-background border-neutral-400',
                success: 'bg-bright-turquoise-100 border-teal-600',
                destructive: 'bg-red-vivid-50 border-red-vivid-600',
                warning: 'bg-fire-bush-100 border-yellow-vivid-600',
                pink: 'bg-pink-vivid-50 border-pink-vivid-600',
                outline: 'bg-background border-neutral-400',
                'light-blue':
                    'bg-light-blue-vivid-50 border-light-blue-vivid-600',
            },
        },
        defaultVariants: {
            variant: 'default',
        },
    },
)

export interface CheckFieldLabelProps
    extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onClick'>,
        VariantProps<typeof checkFieldLabelVariants> {
    /**
     * The type of input to use
     * @default "checkbox"
     */
    type?: 'checkbox' | 'radio'
    /**
     * The ID of the input
     */
    id?: string
    /**
     * Whether the input is checked
     */
    checked?: boolean
    /**
     * Callback when the checked state changes
     */
    onCheckedChange?: (checked: boolean) => void
    /**
     * Whether the input is disabled
     */
    disabled?: boolean
    /**
     * The value of the input (for radio buttons)
     */
    value?: string
    /**
     * The name of the input (for radio buttons)
     */
    name?: string
    /**
     * The label text
     */
    label?: string | React.ReactNode
    /**
     * Children to render inside the label area (overrides label if provided)
     */
    children?: React.ReactNode
    /**
     * Additional className for the label
     */
    className?: string
    /**
     * The radio group value (for radio buttons)
     */
    radioGroupValue?: string
    /**
     * Content to be displayed to the right of the label (e.g., info icon)
     */
    rightContent?: React.ReactNode
    /**
     * Content to be displayed to the left of the label (e.g., icon)
     */
    leftContent?: React.ReactNode
    /**
     * The variant of the checkbox or radio button
     */
    variant?:
        | 'default'
        | 'secondary'
        | 'success'
        | 'destructive'
        | 'pink'
        | 'outline'
        | 'light-blue'
        | 'warning'
        | null
        | undefined
    /**
     * Whether the checkbox should visually appear as a radio button
     * while maintaining checkbox functionality (multiple selection)
     */
    isRadioStyle?: boolean
    /**
     * Additional click handler for the container
     * This will be called after the checkbox/radio is toggled
     */
    onClick?: (event: React.MouseEvent<HTMLDivElement>) => void
}

/**
 * CheckFieldLabel component that combines a checkbox or radio button with a label
 * in a styled container. It can be used for checkboxes or radio buttons.
 */
const CheckFieldLabel = React.forwardRef<HTMLDivElement, CheckFieldLabelProps>(
    (
        {
            type = 'checkbox',
            id,
            checked,
            onCheckedChange,
            disabled,
            value,
            name,
            label,
            children,
            className,
            variant,
            size,
            radioGroupValue,
            isRadioStyle = true,
            rightContent,
            leftContent,
            onClick,
            ...props
        },
        ref,
    ) => {
        // Generate a unique ID if none is provided
        const uniqueId = React.useId()
        const inputId = id || `${type}-${uniqueId}`

        // Determine if radio button is checked based on radioGroupValue
        const isRadioChecked =
            type === 'radio' ? radioGroupValue === value : checked

        // Handle click on the container to toggle checkbox or select radio
        // and call the external onClick handler if provided
        const handleContainerClick = (e: React.MouseEvent<HTMLDivElement>) => {
            if (disabled) return

            // First handle the checkbox/radio toggle
            if (type === 'checkbox' && onCheckedChange) {
                onCheckedChange(!checked)
            } else if (type === 'radio' && onCheckedChange && !isRadioChecked) {
                onCheckedChange(true)
            }

            // Then call the external onClick handler if provided
            if (onClick) {
                onClick(e)
            }
        }

        return (
            <div
                ref={ref}
                className={cn(
                    'flex rounded-lg relative group justify-evenly border border-border shadow-sm cursor-pointer',
                    disabled && 'opacity-50 cursor-not-allowed',
                    className,
                )}
                onClick={handleContainerClick}
                {...props}>
                <div className={cn(innerWrapperVariants({ variant }))}>
                    {type === 'checkbox' ? (
                        <Checkbox
                            id={inputId}
                            isRadioStyle={isRadioStyle}
                            checked={checked}
                            onCheckedChange={(checked) => {
                                // We're making the checkbox non-interactive
                                // The parent container will handle the click
                                if (
                                    typeof checked === 'boolean' &&
                                    onCheckedChange
                                ) {
                                    onCheckedChange(checked)
                                }
                            }}
                            disabled={disabled}
                            name={name}
                            variant={variant}
                            size="lg"
                            className="pointer-events-none"
                        />
                    ) : (
                        <RadioGroupItem
                            id={inputId}
                            value={value || ''}
                            disabled={disabled}
                            variant={variant}
                            size="md"
                            checked={isRadioChecked}
                            className="pointer-events-none"
                        />
                    )}
                </div>
                <div
                    className={cn(
                        'flex items-center',
                        checkFieldLabelVariants({
                            variant: 'secondary',
                            size,
                            disabled,
                        }),
                    )}>
                    <div
                        className={cn('flex flex-1 items-center', {
                            'gap-2': leftContent || rightContent,
                        })}>
                        {leftContent && (
                            <div className="inline-flex items-center">
                                {leftContent}
                            </div>
                        )}
                        {children
                            ? children
                            : label && (
                                  <P
                                      className={cn(
                                          'text-wrap text-foreground text-base',
                                      )}>
                                      {label}
                                  </P>
                              )}
                        {rightContent && (
                            <div className="inline-flex items-center">
                                {rightContent}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        )
    },
)

CheckFieldLabel.displayName = 'CheckFieldLabel'

export { CheckFieldLabel, checkFieldLabelVariants, innerWrapperVariants }
