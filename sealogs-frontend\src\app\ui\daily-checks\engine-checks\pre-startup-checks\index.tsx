'use client'

import { Dispatch } from 'react'
import { composeField, displayDescription, displayField } from '../../actions'
import EngineHour from './engine-hour'
import FuelLevel from './fuel-level'
import PreEngineFields from './pre-engine-fields'
import ElectricalFields from './electrical-fields'
import PreFields from './pre-fields'
import PreEngineOilFields from './pre-engine-oil-fields'
import PreEngineMountFields from './pre-engine-mount-fields'
import {
    CheckField,
    CheckFieldContent,
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import { Card } from '@/components/ui'

interface IProps {
    logentryID: any
    logBookConfig: any
    updateTripReport?: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    loaded: boolean
    offline?: boolean
    fuelTankList: any
    setFuelTankList: Dispatch<any>
    getFuelTanks: Function
    fuelLogs: any
    setFuelLogs: Dispatch<any>
    engineList: any
    setSectionFuelComment: Dispatch<any>
    getComment: (fieldName: string, commentType?: string) => any
    createSectionMemberComment: Function
    updateSectionMemberComment: Function
    setSectionEngineComment: Dispatch<any>
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
    setComments: any
    fieldImages: any
    refreshImages: any
}

export function PreStartupChecks({
    logentryID,
    logBookConfig,
    updateTripReport,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    loaded,
    offline = false,
    fuelTankList,
    setFuelTankList,
    getFuelTanks,
    fuelLogs,
    setFuelLogs,
    engineList,
    setSectionFuelComment,
    getComment,
    createSectionMemberComment,
    updateSectionMemberComment,
    setSectionEngineComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
    setComments,
    fieldImages,
    refreshImages,
}: IProps) {
    return (
        <Card id="engine-pre-startup" className="space-y-8">
            <FuelLevel
                createSectionMemberComment={createSectionMemberComment}
                edit_logBookEntry={edit_logBookEntry}
                fuelLogs={fuelLogs}
                updateTripReport={updateTripReport}
                fuelTankList={fuelTankList}
                getComment={getComment}
                getFuelTanks={getFuelTanks}
                loaded={loaded}
                locked={locked}
                setSectionFuelComment={setSectionFuelComment}
                updateSectionMemberComment={updateSectionMemberComment}
                vesselDailyCheck={vesselDailyCheck}
                offline={offline}
                setFuelLogs={setFuelLogs}
                setFuelTankList={setFuelTankList}
            />
            {engineList?.length > 0 && (
                <EngineHour
                    edit_logBookEntry={edit_logBookEntry}
                    engineList={engineList}
                    vesselDailyCheck={vesselDailyCheck}
                    getComment={getComment}
                    locked={locked}
                    setSectionEngineComment={setSectionEngineComment}
                    setComments={setComments}
                    updateSectionMemberComment={updateSectionMemberComment}
                    createSectionMemberComment={createSectionMemberComment}
                />
            )}
            <CheckField>
                {/* <CheckFieldTopContent /> */}
                <CheckFieldContent>
                    {logBookConfig && vesselDailyCheck && (
                        <>
                            <PreEngineFields
                                edit_logBookEntry={edit_logBookEntry}
                                getComment={getComment}
                                handleEngineChecks={handleEngineChecks}
                                locked={locked}
                                logBookConfig={logBookConfig}
                                setDescriptionPanelContent={
                                    setDescriptionPanelContent
                                }
                                setDescriptionPanelHeading={
                                    setDescriptionPanelHeading
                                }
                                setOpenDescriptionPanel={
                                    setOpenDescriptionPanel
                                }
                                showCommentPopup={showCommentPopup}
                                vesselDailyCheck={vesselDailyCheck}
                                fieldImages={fieldImages}
                                refreshImages={refreshImages}
                            />
                            <PreEngineOilFields
                                edit_logBookEntry={edit_logBookEntry}
                                getComment={getComment}
                                handleEngineChecks={handleEngineChecks}
                                locked={locked}
                                logBookConfig={logBookConfig}
                                setDescriptionPanelContent={
                                    setDescriptionPanelContent
                                }
                                setDescriptionPanelHeading={
                                    setDescriptionPanelHeading
                                }
                                showCommentPopup={showCommentPopup}
                                setOpenDescriptionPanel={
                                    setOpenDescriptionPanel
                                }
                                vesselDailyCheck={vesselDailyCheck}
                                fieldImages={fieldImages}
                                refreshImages={refreshImages}
                            />
                            <PreEngineMountFields
                                edit_logBookEntry={edit_logBookEntry}
                                getComment={getComment}
                                handleEngineChecks={handleEngineChecks}
                                locked={locked}
                                logBookConfig={logBookConfig}
                                setDescriptionPanelContent={
                                    setDescriptionPanelContent
                                }
                                setDescriptionPanelHeading={
                                    setDescriptionPanelHeading
                                }
                                showCommentPopup={showCommentPopup}
                                setOpenDescriptionPanel={
                                    setOpenDescriptionPanel
                                }
                                vesselDailyCheck={vesselDailyCheck}
                                fieldImages={fieldImages}
                                refreshImages={refreshImages}
                            />
                        </>
                    )}
                    <ElectricalFields
                        edit_logBookEntry={edit_logBookEntry}
                        getComment={getComment}
                        handleEngineChecks={handleEngineChecks}
                        locked={locked}
                        logBookConfig={logBookConfig}
                        setDescriptionPanelContent={setDescriptionPanelContent}
                        setOpenDescriptionPanel={setOpenDescriptionPanel}
                        setDescriptionPanelHeading={setDescriptionPanelHeading}
                        vesselDailyCheck={vesselDailyCheck}
                        showCommentPopup={showCommentPopup}
                        fieldImages={fieldImages}
                        refreshImages={refreshImages}
                    />
                    <PreFields
                        edit_logBookEntry={edit_logBookEntry}
                        getComment={getComment}
                        handleEngineChecks={handleEngineChecks}
                        locked={locked}
                        logBookConfig={logBookConfig}
                        logentryID={logentryID}
                        setDescriptionPanelContent={setDescriptionPanelContent}
                        setDescriptionPanelHeading={setDescriptionPanelHeading}
                        setOpenDescriptionPanel={setOpenDescriptionPanel}
                        showCommentPopup={showCommentPopup}
                        vesselDailyCheck={vesselDailyCheck}
                        offline={offline}
                        fieldImages={fieldImages}
                        refreshImages={refreshImages}
                    />
                </CheckFieldContent>
            </CheckField>
        </Card>
    )
}
