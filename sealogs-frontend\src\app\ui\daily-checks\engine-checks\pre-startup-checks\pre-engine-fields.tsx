'use client'

import { Dispatch, useMemo } from 'react'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
    getFilteredFields,
} from '../../actions'
import { useEngineFields } from '../use-engine-fields'
import { AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui'
import {
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import { GET_SECTION_MEMBER_IMAGES } from '@/app/lib/graphQL/query'
import { useSearchParams } from 'next/navigation'
import { useLazyQuery } from '@apollo/client'

interface IProps {
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    getComment: (fieldName: string, commentType?: string) => any
    showCommentPopup: (comment: string, field: any) => void
    fieldImages: any
    refreshImages: any
}

export default function PreEngineFields({
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    setOpenDescriptionPanel,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    handleEngineChecks,
    getComment,
    showCommentPopup,
    fieldImages,
    refreshImages,
}: IProps) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0

    const { preEngineFields } = useEngineFields(logBookConfig, vesselDailyCheck)

    const fields = useMemo(() => {
        return (
            getFilteredFields(preEngineFields, true, logBookConfig)?.filter(
                (groupField: any) =>
                    displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [logBookConfig])



    return (
        <>
            {fields.filter((groupField: any) =>
                groupField?.items?.some((field: any) =>
                    displayField(field.name, logBookConfig),
                ),
            ).length > 0 && <CheckFieldTopContent />}
            {fields.map((groupField: any) => (
                <div key={groupField.name}>
                    {/* <div className="mt-6   uppercase text-left">
                                                    {getFieldLabel(
                                                        groupField.name,
                                                        logBookConfig,
                                                    )}
                                                </div> */}
                    {groupField?.items
                        ?.filter((field: any) =>
                            displayField(field.name, logBookConfig),
                        )
                        ?.map((field: any, index: number) => (
                            <DailyCheckField
                                locked={locked || !edit_logBookEntry}
                                key={index}
                                displayField={displayField(
                                    field.name,
                                    logBookConfig,
                                )}
                                displayDescription={displayDescription(
                                    field.name,
                                    logBookConfig,
                                )}
                                setOpenDescriptionPanel={
                                    setOpenDescriptionPanel
                                }
                                setDescriptionPanelHeading={
                                    setDescriptionPanelHeading
                                }
                                displayLabel={getFieldLabel(
                                    field.name,
                                    logBookConfig,
                                )}
                                inputId={field.value}
                                handleNoChange={() =>
                                    // field.handleChange(
                                    //     false,
                                    // )
                                    handleEngineChecks(false, field.value)
                                }
                                defaultNoChecked={field.checked === 'Not_Ok'}
                                handleYesChange={() =>
                                    // field.handleChange(
                                    //     true,
                                    // )
                                    handleEngineChecks(true, field.value)
                                }
                                defaultYesChecked={field.checked === 'Ok'}
                                commentAction={() =>
                                    showCommentPopup(
                                        getComment(field.name),
                                        composeField(field.name, logBookConfig),
                                    )
                                }
                                comment={getComment(field.name)?.comment}
                                displayImage={true}
                                fieldImages={fieldImages}
                                onImageUpload={refreshImages}
                            />
                        ))}
                    {displayDescription(groupField.name, logBookConfig) && (
                        <Button
                            variant="text"
                            iconLeft={AlertCircle}
                            onClick={() => {
                                setDescriptionPanelContent(
                                    displayDescription(
                                        groupField.name,
                                        logBookConfig,
                                    ),
                                )
                                setOpenDescriptionPanel(true)
                                setDescriptionPanelHeading(groupField.name)
                            }}
                        />
                    )}
                    <>
                        {/* <div className="flex flex-row items-center">
                                                    <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (field: any) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (field: any) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={groupField?.items
                                                            ?.filter((field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Not_Ok',
                                                            )}
                                                        defaultYesChecked={groupField?.items
                                                            ?.filter((field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Ok',
                                                            )}
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    />
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2 hidden`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div> */}
                    </>
                </div>
            ))}
        </>
    )
}
