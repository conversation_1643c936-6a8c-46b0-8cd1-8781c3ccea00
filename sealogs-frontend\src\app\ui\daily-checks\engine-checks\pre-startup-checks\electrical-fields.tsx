'use client'

import { Dispatch, useMemo } from 'react'
import { useEngineFields } from '../use-engine-fields'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
    getFilteredFields,
} from '../../actions'
import { AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui'
import {
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import { useSearchParams } from 'next/navigation'
import { useLazyQuery } from '@apollo/client'
import { GET_SECTION_MEMBER_IMAGES } from '@/app/lib/graphQL/query'

interface IProps {
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    getComment: (fieldName: string, commentType?: string) => any
    showCommentPopup: (comment: string, field: any) => void
    fieldImages: any
    refreshImages: any
}

export default function ElectricalFields({
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    setOpenDescriptionPanel,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    handleEngineChecks,
    getComment,
    showCommentPopup,
    fieldImages,
    refreshImages,
}: IProps) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0

    const { preElectricalFields, preElectricalVisualFields } = useEngineFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const shouldDisplay = useMemo(() => {
        return (
            getFilteredFields(preElectricalFields, true, logBookConfig)?.filter(
                (groupField: any) =>
                    displayField(groupField.name, logBookConfig),
            ).length > 0 ||
            getFilteredFields(
                preElectricalVisualFields,
                true,
                logBookConfig,
            )?.filter((groupField: any) =>
                displayField(groupField.name, logBookConfig),
            ).length > 0 ||
            displayField('Generator', logBookConfig) ||
            displayField('ShorePower', logBookConfig)
        )
    }, [logBookConfig, preElectricalFields, preElectricalVisualFields])

    const filteredPreElectricalFields = useMemo(() => {
        return (
            getFilteredFields(preElectricalFields, true, logBookConfig)?.filter(
                (groupField: any) =>
                    displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [preElectricalFields, logBookConfig])

    const filteredPreElectricalVisualFields = useMemo(() => {
        return (
            getFilteredFields(
                preElectricalVisualFields,
                true,
                logBookConfig,
            )?.filter((groupField: any) =>
                displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [preElectricalVisualFields, logBookConfig])

    if (!shouldDisplay) {
        return <></>
    }



    return (
        <>
            {logBookConfig && vesselDailyCheck && (
                <>
                    {(filteredPreElectricalFields.filter((groupField: any) =>
                        groupField?.items?.some((field: any) =>
                            displayField(field.name, logBookConfig),
                        ),
                    ).length > 0 ||
                        filteredPreElectricalVisualFields.filter(
                            (groupField: any) =>
                                groupField?.items?.some((field: any) =>
                                    displayField(field.name, logBookConfig),
                                ),
                        ).length > 0 ||
                        displayDescription('Generator', logBookConfig) ||
                        displayDescription('ShorePower', logBookConfig)) && (
                        <CheckFieldTopContent />
                    )}
                    {filteredPreElectricalFields.map((groupField: any) => (
                        <div key={groupField.name}>
                            {groupField?.items
                                ?.filter((field: any) =>
                                    displayField(field.name, logBookConfig),
                                )
                                ?.map((field: any, index: number) => (
                                    // <span
                                    //     key={`${field.label}-${index}`}
                                    //     className=" lg:">
                                    //     {index <
                                    //     groupField
                                    //         .items
                                    //         .length -
                                    //         1
                                    //         ? field.label +
                                    //           ' -'
                                    //         : field.label}
                                    //     {displayDescription(
                                    //         field.name,
                                    //         logBookConfig,
                                    //     ) && (
                                    //         <SeaLogsButton
                                    //             icon="alert"
                                    //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                    //             action={() => {
                                    //                 setDescriptionPanelContent(
                                    //                     displayDescription(
                                    //                         field.name,
                                    //                         logBookConfig,
                                    //                     ),
                                    //                 )
                                    //                 setOpenDescriptionPanel(
                                    //                     true,
                                    //                 )
                                    //                 setDescriptionPanelHeading(
                                    //                     field.name,
                                    //                 )
                                    //             }}
                                    //         />
                                    //     )}{' '}
                                    // </span>
                                    <DailyCheckField
                                        locked={locked || !edit_logBookEntry}
                                        key={index}
                                        displayField={displayField(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        displayDescription={displayDescription(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        setOpenDescriptionPanel={
                                            setOpenDescriptionPanel
                                        }
                                        setDescriptionPanelHeading={
                                            setDescriptionPanelHeading
                                        }
                                        displayLabel={getFieldLabel(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        inputId={field.value}
                                        handleNoChange={() =>
                                            // field.handleChange(
                                            //     false,
                                            // )
                                            handleEngineChecks(
                                                false,
                                                field.value,
                                            )
                                        }
                                        defaultNoChecked={
                                            field.checked === 'Not_Ok'
                                        }
                                        handleYesChange={() =>
                                            // field.handleChange(
                                            //     true,
                                            // )
                                            handleEngineChecks(
                                                true,
                                                field.value,
                                            )
                                        }
                                        defaultYesChecked={
                                            field.checked === 'Ok'
                                        }
                                        commentAction={() =>
                                            showCommentPopup(
                                                getComment(field.name),
                                                composeField(
                                                    field.name,
                                                    logBookConfig,
                                                ),
                                            )
                                        }
                                        comment={
                                            getComment(field.name)?.comment
                                        }
                                        displayImage={true}
                                        fieldImages={fieldImages}
                                        onImageUpload={refreshImages}
                                    />
                                ))}
                            {displayDescription(
                                groupField.name,
                                logBookConfig,
                            ) && (
                                <Button
                                    variant="text"
                                    iconLeft={AlertCircle}
                                    onClick={() => {
                                        setDescriptionPanelContent(
                                            displayDescription(
                                                groupField.name,
                                                logBookConfig,
                                            ),
                                        )
                                        setOpenDescriptionPanel(true)
                                        setDescriptionPanelHeading(
                                            groupField.name,
                                        )
                                    }}
                                />
                            )}
                            <>
                                {/* <div className="flex flex-row">
                                                        <DailyCheckGroupField
                                                            locked={
                                                                locked ||
                                                                !edit_logBookEntry
                                                            }
                                                            groupField={groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )}
                                                            handleYesChange={() =>
                                                                handleGroupYesChange(
                                                                    groupField?.items?.filter(
                                                                        (
                                                                            field: any,
                                                                        ) =>
                                                                            displayField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                    ),
                                                                    groupField,
                                                                )
                                                            }
                                                            handleNoChange={() =>
                                                                handleGroupNoChange(
                                                                    groupField?.items?.filter(
                                                                        (
                                                                            field: any,
                                                                        ) =>
                                                                            displayField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                    ),
                                                                    groupField,
                                                                )
                                                            }
                                                            defaultNoChecked={groupField?.items
                                                                ?.filter(
                                                                    (field: any) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                )
                                                                ?.every(
                                                                    (field: any) =>
                                                                        field.checked ===
                                                                        'Not_Ok',
                                                                )}
                                                            defaultYesChecked={groupField?.items
                                                                ?.filter(
                                                                    (field: any) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                )
                                                                ?.every(
                                                                    (field: any) =>
                                                                        field.checked ===
                                                                        'Ok',
                                                                )}
                                                            commentAction={() =>
                                                                showCommentPopup(
                                                                    getComment(
                                                                        groupField.name,
                                                                    ),
                                                                    composeField(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                            }
                                                            comment={
                                                                getComment(
                                                                    groupField.name,
                                                                )?.comment
                                                            }
                                                        />
                                                        {groupField?.items?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    className={`lg:!grid-cols-2 hidden`}
                                                                    innerWrapperClassName={`lg:!col-span-1`}
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    </div> */}
                            </>
                        </div>
                    ))}

                    {filteredPreElectricalVisualFields.map(
                        (groupField: any) => (
                            <div key={groupField.name}>
                                {/* <div className="mt-6   uppercase text-left">
                                                    {getFieldLabel(
                                                        groupField.name,
                                                        logBookConfig,
                                                    )}
                                                </div> */}
                                {groupField?.items
                                    ?.filter((field: any) =>
                                        displayField(field.name, logBookConfig),
                                    )
                                    ?.map((field: any, index: number) => (
                                        // <span
                                        //     key={`${field.label}-${index}`}
                                        //     className=" lg:">
                                        //     {index <
                                        //     groupField
                                        //         .items
                                        //         .length -
                                        //         1
                                        //         ? field.label +
                                        //           ' -'
                                        //         : field.label}
                                        //     {displayDescription(
                                        //         field.name,
                                        //         logBookConfig,
                                        //     ) && (
                                        //         <SeaLogsButton
                                        //             icon="alert"
                                        //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                        //             action={() => {
                                        //                 setDescriptionPanelContent(
                                        //                     displayDescription(
                                        //                         field.name,
                                        //                         logBookConfig,
                                        //                     ),
                                        //                 )
                                        //                 setOpenDescriptionPanel(
                                        //                     true,
                                        //                 )
                                        //                 setDescriptionPanelHeading(
                                        //                     field.name,
                                        //                 )
                                        //             }}
                                        //         />
                                        //     )}{' '}
                                        // </span>
                                        <DailyCheckField
                                            locked={
                                                locked || !edit_logBookEntry
                                            }
                                            key={index}
                                            displayField={displayField(
                                                field.name,
                                                logBookConfig,
                                            )}
                                            displayDescription={displayDescription(
                                                field.name,
                                                logBookConfig,
                                            )}
                                            setOpenDescriptionPanel={
                                                setOpenDescriptionPanel
                                            }
                                            setDescriptionPanelHeading={
                                                setDescriptionPanelHeading
                                            }
                                            displayLabel={getFieldLabel(
                                                field.name,
                                                logBookConfig,
                                            )}
                                            inputId={field.value}
                                            handleNoChange={() =>
                                                // field.handleChange(
                                                //     false,
                                                // )
                                                handleEngineChecks(
                                                    false,
                                                    field.value,
                                                )
                                            }
                                            defaultNoChecked={
                                                field.checked === 'Not_Ok'
                                            }
                                            handleYesChange={() =>
                                                // field.handleChange(
                                                //     true,
                                                // )
                                                handleEngineChecks(
                                                    true,
                                                    field.value,
                                                )
                                            }
                                            defaultYesChecked={
                                                field.checked === 'Ok'
                                            }
                                            commentAction={() =>
                                                showCommentPopup(
                                                    getComment(field.name),
                                                    composeField(
                                                        field.name,
                                                        logBookConfig,
                                                    ),
                                                )
                                            }
                                            comment={
                                                getComment(field.name)?.comment
                                            }
                                            displayImage={true}
                                            fieldImages={fieldImages}
                                            onImageUpload={refreshImages}
                                        />
                                    ))}
                                {displayDescription(
                                    groupField.name,
                                    logBookConfig,
                                ) && (
                                    <Button
                                        variant="text"
                                        iconLeft={AlertCircle}
                                        onClick={() => {
                                            setDescriptionPanelContent(
                                                displayDescription(
                                                    groupField.name,
                                                    logBookConfig,
                                                ),
                                            )
                                            setOpenDescriptionPanel(true)
                                            setDescriptionPanelHeading(
                                                groupField.name,
                                            )
                                        }}
                                    />
                                )}
                                <>
                                    {/* <div className="md:col-span-2">
                                                        <DailyCheckGroupField
                                                            locked={
                                                                locked ||
                                                                !edit_logBookEntry
                                                            }
                                                            groupField={groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )}
                                                            handleYesChange={() =>
                                                                handleGroupYesChange(
                                                                    groupField?.items?.filter(
                                                                        (
                                                                            field: any,
                                                                        ) =>
                                                                            displayField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                    ),
                                                                    groupField,
                                                                )
                                                            }
                                                            handleNoChange={() =>
                                                                handleGroupNoChange(
                                                                    groupField?.items?.filter(
                                                                        (
                                                                            field: any,
                                                                        ) =>
                                                                            displayField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                    ),
                                                                    groupField,
                                                                )
                                                            }
                                                            defaultNoChecked={groupField?.items
                                                                ?.filter(
                                                                    (field: any) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                )
                                                                ?.every(
                                                                    (field: any) =>
                                                                        field.checked ===
                                                                        'Not_Ok',
                                                                )}
                                                            defaultYesChecked={groupField?.items
                                                                ?.filter(
                                                                    (field: any) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                )
                                                                ?.every(
                                                                    (field: any) =>
                                                                        field.checked ===
                                                                        'Ok',
                                                                )}
                                                            commentAction={() =>
                                                                showCommentPopup(
                                                                    getComment(
                                                                        groupField.name,
                                                                    ),
                                                                    composeField(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                            }
                                                            comment={
                                                                getComment(
                                                                    groupField.name,
                                                                )?.comment
                                                            }
                                                        />
                                                        {groupField?.items?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    className={`lg:!grid-cols-2 hidden`}
                                                                    innerWrapperClassName={`lg:!col-span-1`}
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    </div> */}
                                </>
                            </div>
                        ),
                    )}
                    <DailyCheckField
                        locked={locked || !edit_logBookEntry}
                        displayField={displayField('Generator', logBookConfig)}
                        displayDescription={displayDescription(
                            'Generator',
                            logBookConfig,
                        )}
                        setOpenDescriptionPanel={setOpenDescriptionPanel}
                        setDescriptionPanelHeading={setDescriptionPanelHeading}
                        displayLabel={
                            // 'Generator is working as expected'
                            getFieldLabel('Generator', logBookConfig)
                        }
                        className="flex"
                        inputId={'generator'}
                        handleNoChange={() =>
                            handleEngineChecks(false, 'generator')
                        }
                        defaultNoChecked={
                            vesselDailyCheck?.generator === 'Not_Ok'
                        }
                        handleYesChange={() =>
                            handleEngineChecks(true, 'generator')
                        }
                        defaultYesChecked={vesselDailyCheck?.generator === 'Ok'}
                        commentAction={() =>
                            showCommentPopup(
                                getComment('Generator'),
                                composeField('Generator', logBookConfig),
                            )
                        }
                        comment={getComment('Generator')?.comment}
                        displayImage={true}
                        fieldImages={fieldImages}
                        onImageUpload={refreshImages}
                    />
                    <DailyCheckField
                        locked={locked || !edit_logBookEntry}
                        displayField={displayField('ShorePower', logBookConfig)}
                        displayDescription={displayDescription(
                            'ShorePower',
                            logBookConfig,
                        )}
                        setOpenDescriptionPanel={setOpenDescriptionPanel}
                        setDescriptionPanelHeading={setDescriptionPanelHeading}
                        displayLabel={
                            // 'Shore power is disconnected'
                            getFieldLabel('ShorePower', logBookConfig)
                        }
                        className={'flex'}
                        inputId={'shorePower'}
                        handleNoChange={() =>
                            handleEngineChecks(false, 'shorePower')
                        }
                        defaultNoChecked={
                            vesselDailyCheck?.shorePower === 'Not_Ok'
                        }
                        handleYesChange={() =>
                            handleEngineChecks(true, 'shorePower')
                        }
                        defaultYesChecked={
                            vesselDailyCheck?.shorePower === 'Ok'
                        }
                        commentAction={() =>
                            showCommentPopup(
                                getComment('ShorePower'),
                                composeField('ShorePower', logBookConfig),
                            )
                        }
                        comment={getComment('ShorePower')?.comment}
                        displayImage={true}
                        fieldImages={fieldImages}
                        onImageUpload={refreshImages}
                    />
                </>
            )}
        </>
    )
}
