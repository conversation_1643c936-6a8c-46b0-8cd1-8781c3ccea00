'use client'
import React, { useEffect, useState } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import {
    DailyCheckField,
    CheckField,
    CheckFieldTopContent,
    CheckFieldContent,
} from '@/components/daily-check-field'
import { AlertCircle } from 'lucide-react'
import {
    GET_SECTION_MEMBER_COMMENTS,
    GET_SECTION_MEMBER_IMAGES,
    VesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
// useRouter removed as it's no longer needed with ActionFooter
import { useSearchParams } from 'next/navigation'
import {
    displayDescription,
    composeField,
    displayField,
    getF<PERSON>ered<PERSON><PERSON>s,
    getSortOrde<PERSON>,
    getField<PERSON>abel,
} from '@/app/ui/daily-checks/actions'
import 'react-quill/dist/quill.snow.css'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import CrewChecker from './crew-checker/crew-checker'
import dayjs from 'dayjs'
import { Label } from '@/components/ui/label'
import { ActionFooter } from '@/components/ui/action-footer'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { useDebounceFn } from '@reactuses/core'

export default function Cleaning({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    // setTab is unused but kept for API compatibility
    setTab,
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setTab: any
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
    fieldImages: any
    refreshImages: any
}) {
    // Router removed as it's no longer needed with ActionFooter
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const tab = searchParams.get('tab') ?? ''
    const [saving, setSaving] = useState(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    const [sectionComment, setSectionComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')

    // Create wrapper functions to handle type conversion for DailyCheckField props
    const handleSetDescriptionPanelContent = (
        content: string | React.ReactNode,
    ) => {
        setDescriptionPanelContent(content as string)
    }

    const handleSetOpenDescriptionPanel = (open: boolean) => {
        setOpenDescriptionPanel(open)
    }

    const handleSetDescriptionPanelHeading = (heading: string) => {
        setDescriptionPanelHeading(heading)
    }

    const commentModel = new SectionMemberCommentModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    const [cleaningCrewResponsible, setCleaningCrewResponsible] = useState<any>(
        vesselDailyCheck?.cleaningCrewResponsible?.nodes?.map(
            (member: any) => ({
                label: member.firstName + ' ' + member.surname,
                value: member.id,
            }),
        ),
    )

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [tab])

    const [cleaningCheckTime, setCleaningCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.cleaningCheckTime ?? new Date()),
    )
    // State and handlers for exterior/interior are managed through the handleCleanCheck function

    const handleCleanCheck = async (check: Boolean, value: any) => {
        if (+vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck.id,
                [value]: check ? 'Ok' : 'Not_Ok',
            }
            if (offline) {
                const data = await dailyCheckModel.save(variables)
                setVesselDailyCheck([data])
                // setSaving(true)
                setSaving(false)
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        [value]: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing cleaning check', error)
            },
        },
    )

    const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
        VesselDailyCheck_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readVesselDailyCheck_LogBookEntrySections.nodes
                setVesselDailyCheck(data)
                setSaving(true)
                // if (vesselDailyCheck === data[0]) {
                //     // Notification logic here if needed
                // }
            },
            onError: (error: any) => {
                console.error(
                    'VesselDailyCheck_LogBookEntrySection error',
                    error,
                )
            },
        },
    )

    // Tab handling is done through the setTab prop

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({
                    ...variables,
                    id: offlineID,
                })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const fields = [
        {
            name: 'Exterior',
            label: 'Exterior of vessel is clean',
            value: 'exterior',
            checked: vesselDailyCheck?.exterior,
            sortOrder: getSortOrder('Exterior', logBookConfig),
        },
        {
            name: 'Interior',
            label: 'Interior of vessel is clean',
            value: 'interior',
            checked: vesselDailyCheck?.interior,
            sortOrder: getSortOrder('Interior', logBookConfig),
        },
        {
            name: 'CleanGalleyBench',
            label: 'Galley Bench',
            value: 'cleanGalleyBench',
            checked: vesselDailyCheck?.cleanGalleyBench,
            sortOrder: getSortOrder('CleanGalleyBench', logBookConfig),
        },
        {
            name: 'CleanGalleyFloor',
            label: 'Galley Floor',
            value: 'cleanGalleyFloor',
            checked: vesselDailyCheck?.cleanGalleyFloor,
            sortOrder: getSortOrder('CleanGalleyFloor', logBookConfig),
        },
        {
            name: 'CleanTable',
            label: 'Tables',
            value: 'cleanTable',
            checked: vesselDailyCheck?.cleanTable,
            sortOrder: getSortOrder('CleanTable', logBookConfig),
        },
        {
            name: 'CleanMirrorGlass',
            label: 'Mirrors / Glass',
            value: 'cleanMirrorGlass',
            checked: vesselDailyCheck?.cleanMirrorGlass,
            sortOrder: getSortOrder('CleanMirrorGlass', logBookConfig),
        },
        {
            name: 'CleanToilet',
            label: 'Toilets',
            value: 'cleanToilet',
            checked: vesselDailyCheck?.cleanToilet,
            sortOrder: getSortOrder('CleanToilet', logBookConfig),
        },
        {
            name: 'CleanSink',
            label: 'All Sinks',
            value: 'cleanSink',
            checked: vesselDailyCheck?.cleanSink,
            sortOrder: getSortOrder('CleanSink', logBookConfig),
        },
        {
            name: 'CleanDeckFloor',
            label: 'Deck Floors',
            value: 'cleanDeckFloor',
            checked: vesselDailyCheck?.cleanDeckFloor,
            sortOrder: getSortOrder('CleanDeckFloor', logBookConfig),
        },
        {
            name: 'CleanOutsideWallWindow',
            label: 'Outside Walls and Windows',
            value: 'cleanOutsideWallWindow',
            checked: vesselDailyCheck?.cleanOutsideWallWindow,
            sortOrder: getSortOrder('CleanOutsideWallWindow', logBookConfig),
        },
        {
            name: 'CleanGarbageBin',
            label: 'Rubbish / Garbage Bins',
            value: 'cleanGarbageBin',
            checked: vesselDailyCheck?.cleanGarbageBin,
            sortOrder: getSortOrder('CleanGarbageBin', logBookConfig),
        },
        {
            name: 'CleanBoothSeat',
            label: 'Booths / Seats',
            value: 'cleanBoothSeat',
            checked: vesselDailyCheck?.cleanBoothSeat,
            sortOrder: getSortOrder('CleanBoothSeat', logBookConfig),
        },
        {
            name: 'CleanFridge',
            label: 'Fridges',
            value: 'cleanFridge',
            checked: vesselDailyCheck?.cleanFridge,
            sortOrder: getSortOrder('CleanFridge', logBookConfig),
        },
        {
            name: 'CleanCupboard',
            label: 'Cupboards',
            value: 'cleanCupboard',
            checked: vesselDailyCheck?.cleanCupboard,
            sortOrder: getSortOrder('CleanCupboard', logBookConfig),
        },
        {
            name: 'CleanOven',
            label: 'Oven',
            value: 'cleanOven',
            checked: vesselDailyCheck?.cleanOven,
            sortOrder: getSortOrder('CleanOven', logBookConfig),
        },
        {
            name: 'CleanSouvenir',
            label: 'Souvenirs',
            value: 'cleanSouvenir',
            checked: vesselDailyCheck?.cleanSouvenir,
            sortOrder: getSortOrder('CleanSouvenir', logBookConfig),
        },
        {
            name: 'CleanRestockSalesItem',
            label: 'Restock Sales Items',
            value: 'cleanRestockSalesItem',
            checked: vesselDailyCheck?.cleanRestockSalesItem,
            sortOrder: getSortOrder('CleanRestockSalesItem', logBookConfig),
        },
        {
            name: 'CleanTill',
            label: 'Till',
            value: 'cleanTill',
            checked: vesselDailyCheck?.cleanTill,
            sortOrder: getSortOrder('CleanTill', logBookConfig),
        },
    ]

    const handleSave = async () => {
        if (offline) {
            const data = await dailyCheckModel.getByIds([vesselDailyCheck.id])
            setVesselDailyCheck(data)
            setSaving(true)
            // if (vesselDailyCheck === data[0]) {
            //     // Notification logic here if needed
            // }
        } else {
            getSectionVesselDailyCheck_LogBookEntrySection({
                variables: {
                    id: [vesselDailyCheck.id],
                },
            })
        }

        const comment = sectionComment
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: 'Cleaning',
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = getComment('Cleaning', 'Section')
                    ? getComment('Cleaning', 'Section').id
                    : generateUniqueId()
                await commentModel.save({
                    ...variables,
                    id: offlineID,
                })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const handleCleaningCrewResponsible = async (crews: any) => {
        setCleaningCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                cleaningCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log('offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handleCleaningCheckTime = async (date: any) => {
        setCleaningCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                cleaningCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log('offline')
                /* const newVesselDailyCheck =
                                        await dailyCheckModel.save(variables)
                                    // setSaving(true)
                                    setSaving(false)
                                    setVesselDailyCheck([newVesselDailyCheck])
                                    const sections = logbook.logBookEntrySections.nodes
                                    const section = {
                                        className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                                        id: `${vesselDailyCheck.id}`,
                                        logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                                        __typename: 'VesselDailyCheck_LogBookEntrySection',
                                    }
                                    if (
                                        !sections.some(
                                            (s: any) =>
                                                JSON.stringify(s) === JSON.stringify(section),
                                        )
                                    ) {
                                        sections.push(section)
                                    }
                                    const lb = {
                                        ...logbook,
                                        logBookEntrySections: { nodes: sections },
                                    }
                                    await logBookModel.save(lb)
                                    getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        cleaningCheckTime:
                            dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [])

    const saveSectionComment = () => {
        getComment('Cleaning', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('Cleaning', 'Section')?.id,
                          comment: sectionComment,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'Cleaning',
                          comment: sectionComment,
                          logBookEntryID: +logentryID,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)

    return (
        <>
            <Card className="space-y-6">
                {logBookConfig && vesselDailyCheck && (
                    <>
                        <CheckField>
                            {getFilteredFields(
                                fields,
                                false,
                                logBookConfig,
                            ).filter((field: any) =>
                                displayField(field.name, logBookConfig),
                            ).length > 0 && <CheckFieldTopContent />}
                            <CheckFieldContent>
                                {getFilteredFields(
                                    fields,
                                    false,
                                    logBookConfig,
                                ).map((field: any, index: number) => (
                                    <DailyCheckField
                                        locked={!edit_logBookEntry || locked}
                                        key={index}
                                        displayField={displayField(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        displayDescription={displayDescription(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        setDescriptionPanelContent={
                                            handleSetDescriptionPanelContent
                                        }
                                        setOpenDescriptionPanel={
                                            handleSetOpenDescriptionPanel
                                        }
                                        setDescriptionPanelHeading={
                                            handleSetDescriptionPanelHeading
                                        }
                                        displayLabel={getFieldLabel(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        inputId={field.value}
                                        handleNoChange={() =>
                                            // field.handleChange(false)
                                            handleCleanCheck(false, field.value)
                                        }
                                        defaultNoChecked={
                                            field.checked === 'Not_Ok'
                                        }
                                        handleYesChange={() =>
                                            // field.handleChange(true)
                                            handleCleanCheck(true, field.value)
                                        }
                                        defaultYesChecked={
                                            field.checked === 'Ok'
                                        }
                                        commentAction={() =>
                                            showCommentPopup(
                                                getComment(field.name),
                                                composeField(
                                                    field.name,
                                                    logBookConfig,
                                                ),
                                            )
                                        }
                                        comment={
                                            getComment(field.name)?.comment
                                        }
                                        displayImage={true}
                                        fieldImages={fieldImages}
                                        onImageUpload={refreshImages}
                                    />
                                ))}
                            </CheckFieldContent>
                        </CheckField>

                        {getFilteredFields(fields, true, logBookConfig)
                            ?.filter((groupField: any) =>
                                displayField(groupField.name, logBookConfig),
                            )
                            ?.map((groupField: any) => (
                                <CheckField
                                    key={groupField.name}
                                    className="mt-4">
                                    <div className="flex items-center mb-3">
                                        <h3 className="text-lg">
                                            {groupField.field?.title
                                                ? groupField.field.title
                                                : groupField.field.label}
                                        </h3>
                                        {displayDescription(
                                            groupField.name,
                                            logBookConfig,
                                        ) && (
                                            <Button
                                                variant="text"
                                                size="icon"
                                                iconLeft={AlertCircle}
                                                iconOnly
                                                onClick={() => {
                                                    handleSetDescriptionPanelContent(
                                                        displayDescription(
                                                            groupField.name,
                                                            logBookConfig,
                                                        ),
                                                    )
                                                    handleSetOpenDescriptionPanel(
                                                        true,
                                                    )
                                                    handleSetDescriptionPanelHeading(
                                                        groupField.name,
                                                    )
                                                }}
                                            />
                                        )}
                                    </div>
                                    {groupField?.items?.filter((field: any) =>
                                        displayField(field.name, logBookConfig),
                                    ).length > 0 && <CheckFieldTopContent />}
                                    <CheckFieldContent>
                                        {groupField?.items?.map(
                                            (field: any, index: number) => (
                                                <DailyCheckField
                                                    locked={
                                                        !edit_logBookEntry ||
                                                        locked
                                                    }
                                                    key={index}
                                                    displayField={displayField(
                                                        field.name,
                                                        logBookConfig,
                                                    )}
                                                    displayDescription={displayDescription(
                                                        field.name,
                                                        logBookConfig,
                                                    )}
                                                    setDescriptionPanelContent={
                                                        handleSetDescriptionPanelContent
                                                    }
                                                    setOpenDescriptionPanel={
                                                        handleSetOpenDescriptionPanel
                                                    }
                                                    setDescriptionPanelHeading={
                                                        handleSetDescriptionPanelHeading
                                                    }
                                                    displayLabel={getFieldLabel(
                                                        field.name,
                                                        logBookConfig,
                                                    )}
                                                    inputId={field.value}
                                                    handleNoChange={() =>
                                                        // field.handleChange(
                                                        //     false,
                                                        // )
                                                        handleCleanCheck(
                                                            false,
                                                            field.value,
                                                        )
                                                    }
                                                    defaultNoChecked={
                                                        field.checked ===
                                                        'Not_Ok'
                                                    }
                                                    handleYesChange={() =>
                                                        // field.handleChange(
                                                        //     true,
                                                        // )
                                                        handleCleanCheck(
                                                            true,
                                                            field.value,
                                                        )
                                                    }
                                                    defaultYesChecked={
                                                        field.checked === 'Ok'
                                                    }
                                                    commentAction={() =>
                                                        showCommentPopup(
                                                            getComment(
                                                                field.name,
                                                            ),
                                                            composeField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                    }
                                                    comment={
                                                        getComment(field.name)
                                                            ?.comment
                                                    }
                                                    displayImage={true}
                                                    fieldImages={fieldImages}
                                                    onImageUpload={
                                                        refreshImages
                                                    }
                                                />
                                            ),
                                        )}
                                    </CheckFieldContent>
                                </CheckField>
                            ))}
                    </>
                )}

                <CrewChecker
                    vesselDailyCheckID={vesselDailyCheck.id}
                    crewKey="CleaningCrewResponsible"
                    timeKey="CleaningCheckTime"
                    logBookConfig={logBookConfig}
                    locked={locked}
                    offline={offline}
                    edit_logBookEntry={edit_logBookEntry}
                    setCrewResponsible={handleCleaningCrewResponsible}
                    crewResponsible={cleaningCrewResponsible}
                    checkTime={cleaningCheckTime}
                    handleCheckTime={handleCleaningCheckTime}
                    setCheckTime={setCleaningCheckTime}
                />

                <Label label="Comments">
                    <Textarea
                        id={`section_comment`}
                        disabled={locked || !edit_logBookEntry}
                        rows={4}
                        placeholder="Comments on vessel cleanliness..."
                        onChange={(e) => {
                            setSectionComment(e.target.value)
                            debounceSaveSectionComment()
                        }}
                        defaultValue={
                            getComment('Cleaning', 'Section')?.comment
                        }
                    />
                </Label>
            </Card>

            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked}
                actionText="Save">
                <div className="space-y-4">
                    <Label htmlFor="comment">Comment</Label>
                    <Textarea
                        readOnly={locked || !edit_logBookEntry}
                        id="comment"
                        disabled={locked || !edit_logBookEntry}
                        rows={4}
                        placeholder="Comment"
                        defaultValue={
                            currentComment ? currentComment.comment : ''
                        }
                    />
                </div>
            </AlertDialogNew>

            <Sheet
                open={openDescriptionPanel}
                onOpenChange={(open) => {
                    setOpenDescriptionPanel(open)
                    if (!open) {
                        setDescriptionPanelContent('')
                        setDescriptionPanelHeading('')
                    }
                }}>
                <SheetContent side="left" className="w-[90vw] sm:w-[60vw]">
                    <SheetHeader className="space-y-4">
                        <SheetTitle>
                            Field -{' '}
                            <span className="font-light">
                                {descriptionPanelHeading}
                            </span>
                        </SheetTitle>
                    </SheetHeader>

                    <div className="mt-6 overflow-y-auto">
                        <div
                            className="prose prose-sm max-w-none"
                            dangerouslySetInnerHTML={{
                                __html: descriptionPanelContent,
                            }}
                        />
                    </div>
                </SheetContent>
            </Sheet>
        </>
    )
}
