'use client'
import React from 'react'
import dayjs from 'dayjs'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import DatePicker from '../DateRange'
import { Button } from './button'

export default function DateField({
    date = false,
    handleDateChange,
    dateID,
    fieldName = 'Select date',
    buttonLabel = 'Set To Now',
    hideButton = true,
}: {
    date: any
    handleDateChange: any
    dateID: any
    fieldName: any
    buttonLabel?: any
    hideButton?: any
}) {
    /**
     * Normalise the incoming `date` prop (which may be false, a Dayjs
     * instance, a string, or a Date) into a native Date object that the
     * new DatePicker understands.
     */
    const normalisedDate: Date | undefined =
        date && new Date(dayjs(date).toISOString()).getTime() > 0
            ? dayjs(date).toDate()
            : undefined

    return (
        <div className="flex w-full gap-2.5">
            {/* --- Date / Date-Time picker ---------------------------------- */}
            <DatePicker
                id={dateID}
                mode="single"
                type="date"
                placeholder={fieldName}
                value={normalisedDate}
                /**
                 * DatePicker can return:
                 *   • null                        (when cleared)
                 *   • Date                        (single mode)
                 *   • { startDate, endDate }      (range mode)
                 * We only care about a single Date, so unwrap accordingly.
                 */
                onChange={(val) => {
                    if (!val) {
                        handleDateChange(null)
                    } else if (val instanceof Date) {
                        handleDateChange(val)
                    } else if ('startDate' in val && val.startDate) {
                        handleDateChange(val.startDate)
                    } else if ('from' in val && val.from) {
                        handleDateChange(val.from)
                    }
                }}
                className="flex-1"
                clearable
            />

            {/* --- “Set To Now” quick-fill button --------------------------- */}
            {!hideButton && (
                <Button onClick={() => handleDateChange(new Date())}>
                    {buttonLabel}
                </Button>
            )}
        </div>
    )
}
