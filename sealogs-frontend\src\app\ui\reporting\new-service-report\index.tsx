'use client'

import Filter from '@/components/filter'
import { useLazyQuery } from '@apollo/client'
import { useRouter } from 'next/navigation'
import { useMemo, useState } from 'react'
import { ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import ExportButton from '../export-button'
import {
    Table,
    TableCell,
    TableFooter,
    TableHead,
    TableBody,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import {
    GET_VESSELS_WITH_LATEST_STATUS,
    GET_VESSELS_WITH_STATUS_HISTORY,
} from '@/app/lib/graphQL/query/reporting'
import * as dateFns from 'date-fns'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import {
    getLatestStatusFromHistory,
    getServiceSummary,
    IStatusHistory,
    IVesselServiceSummary,
    VesselStatus,
} from './action'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui'
import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'

interface DateRange {
    startDate: Date
    endDate: Date
}

interface IDropdownItem {
    label: string
    value: string
}

interface IReportItem {
    vesselID: number
    vesselName: string
    startingStatus: VesselStatus
    statusHistories: IStatusHistory[]
    statusSummary: IVesselServiceSummary
}

const tableHeadings = [
    'Vessel Name',
    'Voyage (days)',
    'Available for Voyage (days)',
    'Out of Service (days)',
]

const getReportDataSummary = (data: IReportItem[]): IVesselServiceSummary => {
    return data.reduce(
        (prev, current) => {
            let { onVoyage, availableForVoyage, outOfService } = prev

            onVoyage += current.statusSummary.onVoyage
            availableForVoyage += current.statusSummary.availableForVoyage
            outOfService += current.statusSummary.outOfService

            return {
                onVoyage,
                availableForVoyage,
                outOfService,
            }
        },
        { onVoyage: 0, availableForVoyage: 0, outOfService: 0 },
    )
}

const getVesselInService = (
    reportDataSummary: IVesselServiceSummary,
): number => {
    const serviceDays =
        reportDataSummary.onVoyage + reportDataSummary.availableForVoyage
    const totalDays =
        reportDataSummary.onVoyage +
        reportDataSummary.availableForVoyage +
        reportDataSummary.outOfService

    if (totalDays === 0) {
        return 0
    }

    const percentage = ((serviceDays / totalDays) * 100).toFixed(2)

    return parseFloat(percentage)
}

export default function NewServiceReport() {
    const router = useRouter()

    const [reportMode, setReportMode] = useState<'summary' | 'detailed'>(
        'summary',
    )
    const [reportData, setReportData] = useState<IReportItem[]>([])
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(),
        endDate: new Date(),
    })

    const handleFilterOnChange = ({
        type,
        data,
    }: {
        type: 'dateRange' | 'vessels'
        data: any
    }) => {
        switch (type) {
            case 'dateRange':
                setDateRange(data)
                break

            case 'vessels':
                setSelectedVessels(data)
                break

            default:
                break
        }
    }

    const [getReportData, { called, loading }] = useLazyQuery(
        GET_VESSELS_WITH_STATUS_HISTORY,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error('queryLogBookEntrySections error', error)
            },
        },
    )

    const [getVessselsWithLatestStatus] = useLazyQuery(
        GET_VESSELS_WITH_LATEST_STATUS,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error('queryLogBookEntrySections error', error)
            },
        },
    )

    const generateReport = async () => {
        const variables: any = {}

        let vesselFilter = {}

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            variables['statusFilter'] = {
                date: {
                    gte: dateRange.startDate,
                    lte: dateRange.endDate,
                },
            }
        } else {
            setReportData([])
            return
        }

        if (selectedVessels.length > 0) {
            vesselFilter = {
                id: {
                    in: selectedVessels.map((v) => v.value),
                },
                filter: { archived: { eq: false } },
            }

            variables['vesselFilter'] = vesselFilter
        }

        const { data } = await getReportData({
            variables,
        })

        const { data: latestStatusData } = await getVessselsWithLatestStatus({
            variables: {
                vesselFilter,
                statusFilter: {
                    date: {
                        lte: dateRange.startDate,
                    },
                },
            },
        })

        const vessels = data?.readVessels?.nodes ?? []

        if (vessels.length === 0) {
            setReportData([])
            return
        }

        const vesselsWithLatestStatus =
            latestStatusData?.readVessels?.nodes ?? []

        const items: IReportItem[] = vessels.map((vessel: any) => {
            const statusHistories: IStatusHistory[] =
                vessel.statusHistory.nodes.map((status: any) => {
                    const statusItem: IStatusHistory = {
                        id: status.id,
                        date: new Date(status.date),
                        status: status.status,
                    }
                    return statusItem
                })

            const vesselWithLatestStatus = vesselsWithLatestStatus
                .filter((item: any) => item.id == vessel.id)
                .shift()
            const latestStatusHistories =
                vesselWithLatestStatus?.statusHistory?.nodes ?? []
            const latestStatus = getLatestStatusFromHistory(
                latestStatusHistories,
            )

            const item: IReportItem = {
                vesselID: vessel.id,
                vesselName: vessel.title,
                statusHistories: statusHistories,
                startingStatus: latestStatus,
                statusSummary: getServiceSummary(
                    statusHistories,
                    latestStatus,
                    dateRange.startDate!,
                    dateRange.endDate!,
                ),
            }

            return item
        })

        setReportData(items)
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const data: any = reportData.map(function (item) {
            return [
                item.vesselName + '',
                item.statusSummary.onVoyage.toLocaleString(),
                item.statusSummary.availableForVoyage.toLocaleString(),
                item.statusSummary.outOfService.toLocaleString(),
            ]
        })

        exportPdfTable({
            headers: [
                [
                    { content: 'Vessel' },
                    { content: 'On Voyage (days)' },
                    { content: 'Available for Voyage (days)' },
                    { content: 'Out of Service (days)' },
                ],
            ],
            body: data,
            footers: [
                [
                    {
                        content: 'Total',
                    },
                    {
                        content: reportDataSummary.onVoyage.toLocaleString(),
                    },
                    {
                        content:
                            reportDataSummary.availableForVoyage.toLocaleString(),
                    },
                    {
                        content:
                            reportDataSummary.outOfService.toLocaleString(),
                    },
                ],
                [
                    {
                        content: 'In Service',
                    },
                    {
                        content: `${getVesselInService(reportDataSummary)}%`,
                    },
                    {
                        content: '',
                    },
                    {
                        content: '',
                    },
                ],
            ],
            userOptions: {
                showFoot: 'lastPage',
            },
        })
    }

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = []

        csvEntries.push([
            'vessel name',
            'voyage',
            'available for voyage',
            'out of service',
        ])
        reportData.forEach((item) => {
            csvEntries.push([
                item.vesselName,
                item.statusSummary.onVoyage,
                item.statusSummary.availableForVoyage,
                item.statusSummary.outOfService,
            ])
        })

        exportCsv(csvEntries)
    }

    const reportDataSummary = useMemo<IVesselServiceSummary>(() => {
        return getReportDataSummary(reportData)
    }, [reportData])

    return (
        <>
            <ListHeader
                title="Service Report"
                actions={
                    <Button
                        variant="back"
                        iconLeft={ArrowLeft}
                        onClick={() => router.push('/reporting')}>
                        Back
                    </Button>
                }
            />
            <Card className="mt-8">
                <CardHeader>
                    <Tabs
                        value={reportMode}
                        onValueChange={(value) => setReportMode(value as any)}>
                        <TabsList>
                            <TabsTrigger value="summary">
                                Summarized Report
                            </TabsTrigger>
                            <TabsTrigger value="detailed">
                                Detailed Report
                            </TabsTrigger>
                        </TabsList>
                    </Tabs>
                </CardHeader>
                <CardContent className="flex flex-col gap-4">
                    <Filter
                        onChange={handleFilterOnChange}
                        onClick={generateReport}
                    />
                    <ExportButton
                        onDownloadPdf={downloadPdf}
                        onDownloadCsv={downloadCsv}
                    />

                    {reportMode === 'summary' && (
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    {tableHeadings.map((heading) => (
                                        <TableHead key={heading}>
                                            {heading}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            </TableHeader>
                            <ReportTableBody
                                data={reportData}
                                isLoading={called && loading}
                            />
                            {called && loading ? (
                                <></>
                            ) : (
                                <ReportTableSummary
                                    summary={reportDataSummary}
                                />
                            )}
                        </Table>
                    )}

                    {reportMode === 'detailed' && (
                        <>
                            <div className="grid md:grid-cols-4">
                                <div className="grid gap-1">
                                    <div className="font-medium">
                                        Vessels in Service:
                                    </div>
                                    <div>
                                        {getVesselInService(reportDataSummary)}%
                                    </div>
                                </div>
                                <div className="grid gap-1">
                                    <div className="font-medium">
                                        On voyage:
                                    </div>
                                    <div>{reportDataSummary.onVoyage} days</div>
                                </div>
                                <div className="grid gap-1">
                                    <div className="font-medium">
                                        Available for voyage:
                                    </div>
                                    <div>
                                        {reportDataSummary.availableForVoyage}{' '}
                                        days
                                    </div>
                                </div>
                                <div className="grid gap-1">
                                    <div className="font-medium">
                                        Out of Service:
                                    </div>
                                    <div>
                                        {reportDataSummary.outOfService} days
                                    </div>
                                </div>
                            </div>
                            {reportData.map((report) => {
                                return (
                                    <div
                                        key={report.vesselID}
                                        className="bg-[white] shadow rounded-lg border border-border p-4">
                                        <div className="grid md:grid-cols-3 gap-4 mb-3 items-center">
                                            <div>
                                                <h3 className="font-semibold text-lg">
                                                    {report.vesselName}
                                                </h3>
                                            </div>
                                            <div className="md:col-span-2">
                                                <div className="grid md:grid-cols-3">
                                                    <div>
                                                        <span className="font-medium">
                                                            On voyage:
                                                        </span>{' '}
                                                        {
                                                            report.statusSummary
                                                                .onVoyage
                                                        }{' '}
                                                        days
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">
                                                            Available for
                                                            voyage:
                                                        </span>{' '}
                                                        {
                                                            report.statusSummary
                                                                .availableForVoyage
                                                        }{' '}
                                                        days
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">
                                                            Out of Service:
                                                        </span>{' '}
                                                        {
                                                            report.statusSummary
                                                                .outOfService
                                                        }{' '}
                                                        days
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex flex-col gap-2 p-2 border-t border-border">
                                            {/* check if thereis statushistory same with start date then don't prepend starting status */}
                                            {report.statusHistories.filter(
                                                (val) =>
                                                    dateFns.format(
                                                        val.date,
                                                        'dd/MM/yy',
                                                    ) ===
                                                    dateFns.format(
                                                        dateRange.startDate!,
                                                        'dd/MM/yy',
                                                    ),
                                            ).length === 0 && (
                                                <div className="flex gap-4">
                                                    <div>
                                                        {dateFns.format(
                                                            dateRange.startDate!,
                                                            'dd/MM/yy',
                                                        )}
                                                    </div>
                                                    <div>
                                                        {report.startingStatus
                                                            .replace(
                                                                /[A-Z]/g,
                                                                ' $&',
                                                            )
                                                            .slice(1)}
                                                    </div>
                                                </div>
                                            )}
                                            {report.statusHistories.map(
                                                (history) => {
                                                    return (
                                                        <div
                                                            key={history.id}
                                                            className="flex gap-4">
                                                            <div>
                                                                {dateFns.format(
                                                                    history.date,
                                                                    'dd/MM/yy',
                                                                )}
                                                            </div>
                                                            <div>
                                                                {history.status
                                                                    .replace(
                                                                        /[A-Z]/g,
                                                                        ' $&',
                                                                    )
                                                                    .slice(1)}
                                                            </div>
                                                        </div>
                                                    )
                                                },
                                            )}
                                        </div>
                                    </div>
                                )
                            })}
                        </>
                    )}
                </CardContent>
            </Card>
        </>
    )
}

const ReportTableBody = ({
    data,
    isLoading,
}: {
    data: IReportItem[]
    isLoading: boolean
}) => {
    if (isLoading) {
        return (
            <TableBody>
                <TableRow>
                    <TableCell
                        colSpan={tableHeadings.length}
                        className="text-center">
                        Loading...
                    </TableCell>
                </TableRow>
            </TableBody>
        )
    }

    if (data.length === 0) {
        return (
            <TableBody>
                <TableRow>
                    <TableCell
                        colSpan={tableHeadings.length}
                        className="text-center">
                        No Data Found
                    </TableCell>
                </TableRow>
            </TableBody>
        )
    }

    return (
        <TableBody>
            {data.map((value) => (
                <TableRow key={value.vesselID}>
                    <TableCell>{value.vesselName}</TableCell>
                    <TableCell className="text-center">
                        {value.statusSummary.onVoyage}
                    </TableCell>
                    <TableCell className="text-center">
                        {value.statusSummary.availableForVoyage}
                    </TableCell>
                    <TableCell className="text-center">
                        {value.statusSummary.outOfService}
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    )
}

const ReportTableSummary = ({
    summary,
}: {
    summary: IVesselServiceSummary
}) => {
    return (
        <TableFooter>
            <TableRow>
                <TableCell>Total</TableCell>
                <TableCell className="text-center">
                    {summary.onVoyage}
                </TableCell>
                <TableCell className="text-center">
                    {summary.availableForVoyage}
                </TableCell>
                <TableCell className="text-center">
                    {summary.outOfService}
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell>Vessels in Service</TableCell>
                <TableCell className="text-center">
                    {getVesselInService(summary)}%
                </TableCell>
                <TableCell colSpan={2}></TableCell>
            </TableRow>
        </TableFooter>
    )
}
