'use client'

import { Dispatch, useMemo } from 'react'
import { useEngineFields } from '../use-engine-fields'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
    getFilteredFields,
} from '../../actions'
import {
    DailyCheckField,
    CheckField,
    CheckFieldTopContent,
    CheckFieldContent,
} from '@/components/daily-check-field'
import { Button } from '@/components/ui/button'
import { AlertCircle } from 'lucide-react'
import { useSearchParams } from 'next/navigation'
import { useLazyQuery } from '@apollo/client'
import { GET_SECTION_MEMBER_IMAGES } from '@/app/lib/graphQL/query'
interface IProps {
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
    fieldImages: any
    refreshImages: any
}

export default function EngineerElectronicsFields({
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
    fieldImages,
    refreshImages,
}: IProps) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0

    const { engrElectronicsFields } = useEngineFields(
        logBookConfig,
        vesselDailyCheck,
    )

    const filteredFields = useMemo(() => {
        return (
            getFilteredFields(
                engrElectronicsFields,
                true,
                logBookConfig,
            )?.filter((groupField: any) =>
                displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [logBookConfig, engrElectronicsFields])



    return (
        <>
            {logBookConfig && vesselDailyCheck && (
                <CheckField>
                    <CheckFieldContent>
                        {filteredFields.map((groupField: any) => (
                            <div key={groupField.name}>
                                {groupField?.items
                                    ?.filter((field: any) =>
                                        displayField(field.name, logBookConfig),
                                    )
                                    ?.map((field: any, index: number) => (
                                        <DailyCheckField
                                            locked={
                                                locked || !edit_logBookEntry
                                            }
                                            key={index}
                                            displayField={displayField(
                                                field.name,
                                                logBookConfig,
                                            )}
                                            displayDescription={displayDescription(
                                                field.name,
                                                logBookConfig,
                                            )}
                                            setDescriptionPanelContent={
                                                setDescriptionPanelContent as (
                                                    content:
                                                        | string
                                                        | React.ReactNode,
                                                ) => void
                                            }
                                            setOpenDescriptionPanel={
                                                setOpenDescriptionPanel
                                            }
                                            setDescriptionPanelHeading={
                                                setDescriptionPanelHeading
                                            }
                                            displayLabel={getFieldLabel(
                                                field.name,
                                                logBookConfig,
                                            )}
                                            inputId={field.value}
                                            handleNoChange={() =>
                                                handleEngineChecks(
                                                    false,
                                                    field.value,
                                                )
                                            }
                                            defaultNoChecked={
                                                field.checked === 'Not_Ok'
                                            }
                                            handleYesChange={() =>
                                                handleEngineChecks(
                                                    true,
                                                    field.value,
                                                )
                                            }
                                            defaultYesChecked={
                                                field.checked === 'Ok'
                                            }
                                            commentAction={() =>
                                                showCommentPopup(
                                                    getComment(field.name),
                                                    composeField(
                                                        field.name,
                                                        logBookConfig,
                                                    ),
                                                )
                                            }
                                            comment={
                                                getComment(field.name)?.comment
                                            }
                                            displayImage={true}
                                            fieldImages={fieldImages}
                                            onImageUpload={refreshImages}
                                        />
                                    ))}
                                {displayDescription(
                                    groupField.name,
                                    logBookConfig,
                                ) && (
                                    <Button
                                        variant="text"
                                        iconOnly
                                        iconLeft={AlertCircle}
                                        onClick={() => {
                                            setDescriptionPanelContent(
                                                displayDescription(
                                                    groupField.name,
                                                    logBookConfig,
                                                ),
                                            )
                                            setOpenDescriptionPanel(true)
                                            setDescriptionPanelHeading(
                                                groupField.name,
                                            )
                                        }}
                                    />
                                )}
                            </div>
                        ))}
                    </CheckFieldContent>
                </CheckField>
            )}
        </>
    )
}
