'use client'

import { VESSEL_BRIEF_LIST } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'

import { useEffect, useState } from 'react'
import { Combobox } from '@/components/ui/comboBox'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'

const VesselDropdown = ({
    value,
    onChange,
    isClearable = false,
    className = '',
    vesselIdOptions = [],
    filterByTrainingSessionMemberId = 0,
}: any) => {
    const [isLoading, setIsLoading] = useState(true)
    const [vesselList, setVesselList] = useState([] as any)
    const [allVesselList, setAllVesselList] = useState([] as any)
    const [selectedVessel, setSelectedVessel] = useState([] as any)
    const [rawVesselData, setRawVesselData] = useState([] as any)
    const { getVesselWithIcon, loading: vesselIconLoading } =
        useVesselIconData()
    const [queryVesselList, { loading: queryVesselListLoading }] = useLazyQuery(
        VESSEL_BRIEF_LIST,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readVessels.nodes

                if (data) {
                    const filteredData = data.filter(
                        (vessel: any) => !vessel.archived && vessel.title,
                    )
                    setRawVesselData(filteredData)
                }
            },
            onError: (error: any) => {
                console.error('queryVesselList error', error)
            },
        },
    )
    const loadVesselList = async () => {
        let filter = {}
        if (filterByTrainingSessionMemberId > 0) {
            filter = {
                trainingSessions: {
                    members: {
                        id: { contains: filterByTrainingSessionMemberId },
                    },
                },
            }
        }
        filter = {
            ...filter,
            archived: { eq: false },
        }
        queryVesselList({
            variables: { filter: filter },
        })
    }
    // Process raw vessel data when vessel icon data is ready
    useEffect(() => {
        if (rawVesselData.length > 0 && !vesselIconLoading) {
            const formattedData = rawVesselData.map((vessel: any) => {
                const vesselWithIcon = getVesselWithIcon(vessel.id, vessel)
                return {
                    value: vessel.id,
                    label: vessel.title,
                    vessel: vesselWithIcon,
                }
            })
            formattedData.sort((a: any, b: any) =>
                a.label.localeCompare(b.label),
            )
            setAllVesselList(formattedData)
            setVesselList(formattedData)
        }
    }, [rawVesselData, vesselIconLoading])

    useEffect(() => {
        if (isLoading) {
            loadVesselList()
            setIsLoading(false)
        }
    }, [isLoading])
    useEffect(() => {
        if (vesselList.length > 0) {
            setSelectedVessel(
                vesselList.find((vessel: any) => vessel.value === value),
            )
        }
    }, [value, vesselList])
    useEffect(() => {
        if (vesselIdOptions.length > 0) {
            const filteredVesselList = allVesselList.filter((v: any) =>
                vesselIdOptions.includes(v.value),
            )
            setVesselList(filteredVesselList)
        } else {
            // If no options are provided, show the full list
            setVesselList(allVesselList)
        }
    }, [vesselIdOptions, allVesselList])

    return (
        <Combobox
            options={vesselList}
            defaultValues={selectedVessel}
            onChange={(selectedOption: any) => {
                setSelectedVessel(selectedOption)
                onChange(selectedOption)
            }}
            isLoading={queryVesselListLoading && vesselList && !isLoading}
            title="Vessel"
            buttonClassName={className}
            labelClassName={className}
            placeholder="Vessel"
            multi={false}
        />
    )
}

export default VesselDropdown
