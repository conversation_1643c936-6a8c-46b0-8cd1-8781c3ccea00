'use client'
import Link from 'next/link'
import { createColumns, DataTable } from '@/components/filteredTable'
import { usePathname, useSearchParams } from 'next/navigation'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { useIsMobile } from '@/components/hooks/use-mobile'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { StatusBadge } from '@/app/ui/maintenance/list/list'

import { Card, CardContent } from '@/components/ui/card'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import dayjs from 'dayjs'

// Helper functions to match TaskList visual appearance
const getCrewInitials = (firstName?: string, surname?: string): string => {
    if (!firstName && !surname) return '??'
    const first = firstName?.charAt(0)?.toUpperCase() || ''
    const last = surname?.charAt(0)?.toUpperCase() || ''
    return `${first}${last}` || '??'
}

const getCrewDetails = (assignedToID: string, crewInfo?: CrewMember[]) => {
    return crewInfo?.find((crew) => crew.id === assignedToID)
}

// Transform MaintenanceTask to MaintenanceCheck for StatusBadge compatibility
const transformToMaintenanceCheck = (
    task: MaintenanceTask,
): MaintenanceCheck => {
    return {
        id: parseInt(task.id),
        assignedTo: {
            id: parseInt(task.assignedTo?.id || '0'),
            name: task.assignedTo?.name || '',
        },
        basicComponent: {
            id: 0,
            title: null,
        },
        inventory: {
            id: parseInt(task.inventory?.id || '0'),
            item: task.inventory?.item || null,
        },
        status: task.isOverDue?.status || '',
        recurringID: 0,
        name: task.name,
        created: new Date().toISOString(),
        severity: task.severity,
        isOverDue: {
            status: task.isOverDue?.status || '',
            days: task.isOverDue?.days || '',
            ignore: false,
            day: 0,
        },
        comments: null,
        workOrderNumber: null,
        startDate: new Date().toISOString(),
        expires: null,
        maintenanceCategoryID: 0,
    }
}

interface MaintenanceTask {
    id: string
    name: string
    severity: string
    assignedToID: string
    inventoryID?: string
    assignedTo?: {
        id: string
        name: string
    }
    inventory?: {
        id: string
        item: string
    }
    isOverDue?: {
        status: string
        days: string
    }
}

interface CrewMember {
    id: string
    firstName: string
    surname: string
}

// MaintenanceCheck interface to match StatusBadge requirements
interface MaintenanceCheck {
    id: number
    assignedTo: {
        id: number
        name: string
    }
    basicComponent: {
        id: number
        title: string | null
    }
    inventory: {
        id: number
        item: string | null
    }
    status: string
    recurringID: number
    name: string
    created: string
    severity: string
    isOverDue: {
        status: string
        days: string
        ignore: boolean
        day: number
    }
    comments: string | null
    workOrderNumber: string | null
    startDate: string
    expires: string | null
    maintenanceCategoryID: number
}

export default function MaintenanceTab({
    maintenanceTasks,
    crewInfo,
}: {
    maintenanceTasks: MaintenanceTask[]
    crewInfo: CrewMember[]
}) {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const isMobile = useIsMobile()
    const [visibleTasksCount, setVisibleTasksCount] = useState(5)

    const columns = createColumns<MaintenanceTask>([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Title" />
            ),
            cell: ({ row }: { row: any }) => {
                const task = row.original as MaintenanceTask

                return (
                    <>
                        {/* Show card layout on xs devices */}
                        <Card className="xs:hidden space-y-3 p-2.5 min-h-20 border-b shadow-none rounded-none bg-transparent">
                            <CardContent className="p-0 space-y-3">
                                {/* Task Title */}
                                <div className="flex gap-2.5 justify-between">
                                    <Link
                                        href={`/maintenance?taskID=${task.id}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`}
                                        className="text-base">
                                        {task.name ??
                                            `Task #${task.id} (No Name) - ${dayjs().format('DD/MM/YYYY')}`}
                                    </Link>
                                    <div>
                                        <StatusBadge
                                            maintenanceCheck={transformToMaintenanceCheck(
                                                task,
                                            )}
                                        />
                                    </div>
                                </div>

                                {/* Assigned To */}
                                {task.assignedTo?.id &&
                                    task.assignedTo.id !== '0' && (
                                        <div className="flex items-center gap-2.5">
                                            <Avatar
                                                variant="secondary"
                                                className="h-8 w-8">
                                                <AvatarFallback className="text-xs">
                                                    {getCrewDetails(
                                                        task.assignedToID,
                                                        crewInfo,
                                                    )
                                                        ? getCrewInitials(
                                                              getCrewDetails(
                                                                  task.assignedToID,
                                                                  crewInfo,
                                                              )?.firstName,
                                                              getCrewDetails(
                                                                  task.assignedToID,
                                                                  crewInfo,
                                                              )?.surname,
                                                          )
                                                        : getCrewInitials(
                                                              task.assignedTo.name.split(
                                                                  ' ',
                                                              )[0],
                                                              task.assignedTo.name
                                                                  .split(' ')
                                                                  .slice(1)
                                                                  .join(' '),
                                                          )}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div className="flex flex-col">
                                                <span className="text-xs text-muted-foreground">
                                                    Assigned to
                                                </span>
                                                <Link
                                                    href={`/crew/info?id=${task.assignedTo.id}`}
                                                    className="hover:underline text-sm">
                                                    {task.assignedTo.name}
                                                </Link>
                                            </div>
                                        </div>
                                    )}
                                {/* Inventory Item */}
                                {task.inventory?.id &&
                                    parseInt(task.inventory.id) > 0 && (
                                        <div className="flex flex-col">
                                            <span className="text-xs text-muted-foreground">
                                                Inventory item
                                            </span>
                                            <Link
                                                href={`/inventory/view?id=${task.inventory.id}`}
                                                className="hover:underline text-sm">
                                                {task.inventory.item}
                                            </Link>
                                        </div>
                                    )}
                            </CardContent>
                        </Card>

                        {/* Show normal table layout on larger devices */}
                        <div className="hidden xs:block">
                            <div className="flex items-center flex-nowrap gap-2">
                                <Link
                                    href={`/maintenance?taskID=${task.id}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`}>
                                    {task.name ??
                                        `Task #${task.id} (No Name) - ${dayjs().format('DD/MM/YYYY')}`}
                                </Link>
                            </div>
                        </div>
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'assigned',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Assigned to" />
            ),
            cellAlignment: 'left',
            breakpoint: 'tablet-md',
            cell: ({ row }: { row: any }) => {
                const task = row.original as MaintenanceTask
                const crewDetails = getCrewDetails(task.assignedToID, crewInfo)

                return (
                    <>
                        {task.assignedTo?.id && task.assignedTo.id !== '0' ? (
                            <div className="flex items-center gap-2.5">
                                <Avatar variant="secondary" className="h-8 w-8">
                                    <AvatarFallback className="text-xs">
                                        {crewDetails
                                            ? getCrewInitials(
                                                  crewDetails.firstName,
                                                  crewDetails.surname,
                                              )
                                            : getCrewInitials(
                                                  task.assignedTo.name.split(
                                                      ' ',
                                                  )[0],
                                                  task.assignedTo.name
                                                      .split(' ')
                                                      .slice(1)
                                                      .join(' '),
                                              )}
                                    </AvatarFallback>
                                </Avatar>
                                <Link
                                    href={`/crew/info?id=${task.assignedTo.id}`}
                                    className="hover:underline hidden tablet-md:block">
                                    {task.assignedTo.name}
                                </Link>
                            </div>
                        ) : (
                            <span>-</span>
                        )}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.assignedTo?.name || ''
                const valueB = rowB?.original?.assignedTo?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'inventory',
            header: 'Inventory item',
            cellAlignment: 'left',
            breakpoint: 'phablet',
            cell: ({ row }: { row: any }) => {
                const task = row.original as MaintenanceTask
                return (
                    <>
                        {task.inventory?.id &&
                        parseInt(task.inventory.id) > 0 ? (
                            <Link
                                href={`/inventory/view?id=${task.inventory.id}`}
                                className="hover:underline">
                                {task.inventory.item}
                            </Link>
                        ) : (
                            <span>-</span>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'status',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Status" />
            ),
            cellAlignment: 'right',
            breakpoint: 'phablet',
            cell: ({ row }: { row: any }) => {
                const task = row.original as MaintenanceTask

                if (!task) {
                    return <div>-</div>
                }

                return (
                    <>
                        <StatusBadge
                            maintenanceCheck={transformToMaintenanceCheck(task)}
                        />
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.isOverDue?.days || ''
                const valueB = rowB?.original?.isOverDue?.days || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    // Get tasks to display based on screen size and load more functionality
    const tasksToDisplay = isMobile
        ? maintenanceTasks?.slice(0, visibleTasksCount) || []
        : maintenanceTasks || []

    return (
        <div>
            {maintenanceTasks?.length > 0 ? (
                <>
                    <DataTable
                        columns={columns}
                        data={tasksToDisplay}
                        showToolbar={false}
                    />
                    {/* Load More button - only show on mobile when there are more tasks */}
                    {isMobile &&
                        visibleTasksCount < maintenanceTasks.length && (
                            <div className="flex justify-center mt-4">
                                <Button
                                    variant="outline"
                                    onClick={() =>
                                        setVisibleTasksCount((prev) =>
                                            Math.min(
                                                prev + 20,
                                                maintenanceTasks.length,
                                            ),
                                        )
                                    }
                                    className="w-full max-w-sm">
                                    View More
                                </Button>
                            </div>
                        )}
                </>
            ) : (
                <div className="flex justify-center items-center h-96">
                    <div className="flex flex-col items-center">
                        <div className="flex justify-between items-center gap-2 p-2 pt-4">
                            <div>
                                <svg
                                    className="!w-[100px] h-auto"
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 148.02 147.99">
                                    <path
                                        d="M70.84.56c16-.53,30.66,3.59,43.98,12.35,12.12,8.24,21.1,19.09,26.92,32.55,6.14,14.85,7.38,30.11,3.74,45.78-3.92,15.59-11.95,28.57-24.1,38.96-13.11,10.9-28.24,16.66-45.39,17.28-16.75.33-31.88-4.39-45.39-14.17-13.29-9.92-22.34-22.84-27.16-38.76-4.03-14.16-3.9-28.29.39-42.38,5-15.45,14-27.97,27.01-37.6C42.77,5.97,56.1,1.31,70.84.56Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        stroke="#024450"
                                        strokeMiterlimit="10"
                                        strokeWidth="1.02px"
                                    />
                                    <path
                                        d="M63.03,13.61c1.74.02,3.47.13,5.19.32,2.15.26,4.31.51,6.46.78,1.18.34,2.08,1.04,2.69,2.11.56,1,.85,2.06.87,3.2,1.5,2.89,2.99,5.79,4.47,8.69.09.17.19.32.32.46,1.72,1.08,3.12,2.48,4.2,4.2.42.79.72,1.63.9,2.5-.04.01-.07.04-.1.07.58,1.01.64,2.04.17,3.11-.47.88-1.1,1.62-1.92,2.21-1.17.81-2.44,1.45-3.79,1.92-.07.56-.13,1.13-.17,1.7,0,.86-.03,1.72-.1,2.57-.14.56-.42,1.04-.85,1.43-.38.3-.8.39-1.26.27-.01,1.92-.46,3.73-1.33,5.44-.59,2.66-1.36,5.27-2.33,7.82-.4,1.04-.96,1.99-1.67,2.84-.36-.12-.73-.2-1.12-.27-.28,0-.53.08-.78.22-.23.16-.45.33-.68.49-.83.87-1.67,1.73-2.52,2.57-.78.67-1.68,1.03-2.72,1.09-.09-.26-.18-.52-.27-.78-.26-.26-.58-.43-.95-.51-1.68-.23-3.27.06-4.76.87-.28.24-.56.48-.85.7-.95-1.87-2.36-3.27-4.25-4.2-.37-.14-.74-.25-1.12-.34-.42-.03-.84-.03-1.26,0-.19.06-.38.1-.58.1-.58-.66-1.04-1.39-1.38-2.21-1.11-2.73-1.98-5.53-2.62-8.4-.89-1.7-1.33-3.51-1.33-5.44-.97.14-1.64-.25-2.01-1.17-.12-.3-.2-.6-.24-.92-.01-.76-.03-1.52-.05-2.28-.02-.39-.07-.78-.15-1.17-1.41-.47-2.77-1.07-4.05-1.82-.82-.49-1.54-1.09-2.16-1.82-.66-.81-.93-1.73-.83-2.77.33-1.03.65-2.06.92-3.11.56-1.18,1.32-2.22,2.26-3.13,1.27-1.15,2.67-2.11,4.2-2.89,1.39-2.69,2.79-5.37,4.17-8.06.01-1.77.66-3.26,1.92-4.49.47-.39,1-.67,1.6-.83,3.29-.42,6.57-.79,9.85-1.09Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M63.17,14.97c2.44.07,4.86.25,7.28.56,1.3.16,2.59.33,3.88.49.85.26,1.5.78,1.92,1.58.43.87.64,1.79.63,2.77,1.18,2.31,2.37,4.62,3.57,6.92-3.88-1.88-7.97-3.04-12.28-3.5-5.82-.65-11.53-.15-17.14,1.5-1.08.33-2.13.73-3.16,1.19l-.05-.05c1.01-2.01,2.04-4.02,3.08-6.02,0-1.18.3-2.26.92-3.25.41-.57.95-.95,1.63-1.14,3.23-.44,6.47-.78,9.71-1.04Z"
                                        fill="#2998e9"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M22.83,121.38c-.05.7-.06,1.42-.05,2.14h-1.31v-1.84c.04-6.98.54-13.92,1.48-20.82.54-4.01,1.44-7.94,2.67-11.8.83-2.63,2.05-5.06,3.64-7.28,1.23-1.49,2.67-2.74,4.32-3.74,0-.15-.03-.29-.12-.41,3.43-.91,6.85-1.76,10.29-2.55,2.46,6.94,4.9,13.88,7.33,20.82h25.63c2.42-6.97,4.87-13.93,7.35-20.87,1.78.46,3.56.91,5.34,1.36,1.34-2.25,3.04-4.21,5.1-5.87.78-4.96,2.07-9.78,3.88-14.47.65-1.62,1.43-3.17,2.33-4.66.76-1.21,1.68-2.27,2.79-3.18-1.36-.17-2.34-.88-2.94-2.11-.04-.09-.06-.19-.07-.29-2.47-.68-3.87-2.31-4.2-4.85-.2-2.64-.39-5.28-.58-7.91-.03-.54,0-1.09.07-1.63-.17-1.88.57-3.25,2.23-4.13,1.68-.73,3.36-1.46,5.05-2.18.39-.11.79-.17,1.19-.17,3.64.42,7.27.88,10.9,1.38,1.72.41,2.66,1.5,2.82,3.25-.02,1.36-.63,2.38-1.8,3.06,1.1,1.14,1.33,2.44.7,3.91-.33.64-.82,1.14-1.43,1.5,1.22,1.38,1.34,2.85.36,4.42-.31.42-.69.75-1.14,1,1.02,1.05,1.29,2.27.8,3.66-.77,1.59-2.04,2.3-3.81,2.11-.7-.09-1.39-.17-2.09-.24,1.17,1.13,2.15,2.4,2.94,3.81,1.95,3.61,3.36,7.43,4.22,11.46,2.2.83,4.31,1.85,6.33,3.03.89.53,1.66,1.2,2.31,2.01.7,1.3,1.09,2.69,1.17,4.17.08,2.03-.09,4.03-.53,6.02-.48,2.16-1.04,4.3-1.7,6.41-.79,2.37-1.56,4.75-2.33,7.14-.74.36-1.49.39-2.26.07-1.22-.53-2.31-1.25-3.28-2.16-1.78,5.28-4.16,10.26-7.14,14.95-.02.04-.03.09-.02.15,3.62.73,6.54,2.56,8.76,5.49,1.2,1.7,1.84,3.59,1.92,5.68,0,.23-.01.45-.02.68-.42.42-.93.64-1.53.66-1.25.03-2.48-.12-3.69-.44-2.04-.52-4.08-1.05-6.12-1.6-.88-.23-1.78-.37-2.69-.41-.84.03-1.68.16-2.5.36-1.96.52-3.91,1.04-5.87,1.55-.95.21-1.9.39-2.86.53-.49.03-.97.03-1.46,0-.49-.08-.9-.3-1.24-.66-.08-2.31.54-4.41,1.84-6.31,1.21-1.71,2.74-3.06,4.59-4.05.75-.38,1.51-.72,2.28-1.04-2.93-4.67-5.04-9.68-6.33-15.05-.58-2.67-.91-5.37-.97-8.11-.39.24-.79.48-1.19.7-.06.04-.1.1-.12.17-1.41,3.89-2.79,7.79-4.15,11.7h1.02c1.11,12.83,2.22,25.66,3.35,38.49h-56.89c1.1-12.83,2.22-25.66,3.35-38.49.39.01.78,0,1.17-.05-1.95-5.48-3.88-10.97-5.8-16.46-.03-.04-.08-.05-.12-.02-1.95,1.22-3.53,2.82-4.73,4.78-1.06,1.86-1.92,3.82-2.57,5.87-.84,2.72-1.51,5.49-1.99,8.3-.9,5.53-1.47,11.1-1.7,16.7-.09,2.12-.15,4.24-.17,6.36Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M60.99,25.7c4.24-.18,8.43.18,12.57,1.09,2.09.5,4.11,1.17,6.07,2.04,2.05.9,3.86,2.16,5.41,3.76.3.38.58.77.85,1.17-1.92-1.08-3.96-1.91-6.12-2.5-4.32-1.11-8.7-1.74-13.15-1.89-5.41-.23-10.78.09-16.12.97-2.72.53-5.36,1.34-7.91,2.43-.62.33-1.24.65-1.84.97.76-1.17,1.71-2.16,2.86-2.96,2.19-1.5,4.57-2.61,7.14-3.35,3.35-.98,6.76-1.56,10.24-1.72Z"
                                        fill="#fdfdfd"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M103.75,26.28c1.16-.16,2.11.22,2.84,1.12.64,1.04.61,2.06-.1,3.06-.2.24-.44.44-.7.61-1.53.69-3.07,1.37-4.61,2.04-.38.15-.77.28-1.17.39-.11.09-.19.19-.27.32,0,.77.24,1.45.73,2.04.29.28.59.53.9.78-1.35,1.23-1.62,2.67-.8,4.32.28.46.65.84,1.09,1.14-.75.57-1.19,1.32-1.31,2.26-1.73-.68-2.64-1.96-2.74-3.83-.19-2.49-.37-4.98-.53-7.48.06-.89.08-1.78.05-2.67.18-.77.61-1.36,1.29-1.77,1.78-.79,3.56-1.55,5.34-2.31Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M107.73,26.67c2.3.3,4.59.6,6.89.9,1.21.16,1.87.84,1.99,2.04-.12,1.31-.83,2-2.16,2.06-2.2-.25-4.39-.54-6.58-.87.52-1.02.63-2.09.32-3.2-.13-.33-.28-.63-.46-.92Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M51.08,48.56c-.66-.05-1.32-.06-1.99-.05v-6.02c1.29-1.06,2.2-2.39,2.74-3.98.79-2.34,1.25-4.76,1.38-7.23,6.35-.8,12.71-.84,19.08-.12.66.1,1.33.2,1.99.29.15,1.96.45,3.89.9,5.8.37,1.45.98,2.79,1.8,4.03.23.32.49.61.75.9.25.22.52.42.8.61.02,1.91.05,3.82.07,5.73-.65,0-1.3,0-1.94.02-1.31,1.17-2.84,1.72-4.61,1.65-.6,0-1.11-.24-1.5-.68-4.45-.03-8.9-.03-13.35,0-.2.29-.48.47-.83.53-2.01.37-3.77-.12-5.29-1.48Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M51.62,31.57h.19v.29c-.15,2.42-.67,4.75-1.58,6.99-.28.64-.65,1.22-1.09,1.75-.05-2.84-.06-5.69-.05-8.54.83-.19,1.67-.35,2.52-.49Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M75.7,31.77c.93.14,1.85.32,2.77.53,0,2.88,0,5.76-.02,8.64-.59-.73-1.06-1.54-1.41-2.43-.77-2.18-1.21-4.43-1.33-6.75Z"
                                        fill="#fdfdfd"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M106.67,32.06c2.43.31,4.85.63,7.28.95,1.17.17,1.82.84,1.94,2.01-.13,1.26-.82,1.96-2.09,2.09-3.63-.46-7.25-.92-10.87-1.38-.76-.11-1.33-.5-1.7-1.17,1.57-.72,3.16-1.42,4.76-2.09.25-.1.48-.24.68-.41Z"
                                        fill="#fdfdfd"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M47.59,32.45c.06.5.1,1.02.1,1.55s-.01,1.04-.05,1.55c-1.54-.26-2.47.37-2.79,1.89-.05.4-.07.81-.07,1.21.04,1.09.13,2.17.24,3.25-.01.06-.03.13-.05.19-1.51-.5-2.9-1.22-4.17-2.16-1.83-1.54-1.81-3.06.05-4.56,1.6-1.13,3.35-1.97,5.24-2.52.5-.14,1-.28,1.5-.41Z"
                                        fill="#fdfdfd"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M80.02,32.74c1.93.51,3.72,1.32,5.39,2.4.65.47,1.17,1.04,1.58,1.72.26.66.21,1.29-.15,1.89-.26.41-.58.77-.95,1.09-.99.74-2.05,1.35-3.2,1.82-.01-.07-.03-.15-.05-.22.14-1.25.2-2.5.17-3.76-.23-1.67-1.18-2.38-2.84-2.14-.01-.95,0-1.88.05-2.82Z"
                                        fill="#fdfdfd"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M46.76,36.82c.28-.06.5.02.66.24.11.21.19.44.24.68.03,3.02.03,6.05,0,9.08-.02.32-.12.61-.29.87-.2.21-.36.17-.49-.1-.08-.16-.15-.32-.19-.49,0-1.69-.11-3.37-.34-5.05-.07-.92-.14-1.84-.19-2.77-.03-.52-.03-1.03,0-1.55.03-.43.24-.74.61-.92Z"
                                        fill="#fdfdfd"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M80.4,36.82c.54-.08.87.15,1,.68.05.39.08.78.07,1.17-.12,2.11-.29,4.21-.51,6.31-.01.69-.03,1.39-.05,2.09-.31,1.03-.61,1.03-.92,0-.03-3.14-.03-6.28,0-9.42.04-.33.18-.6.41-.83Z"
                                        fill="#fdfdfd"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M103.12,37.2c.55,0,1.1.03,1.65.12,3,.38,5.99.79,8.98,1.21,1.03.45,1.48,1.23,1.33,2.35-.34,1.04-1.06,1.57-2.16,1.6-3.32-.39-6.64-.83-9.95-1.29-1.32-.53-1.76-1.48-1.33-2.84.34-.58.84-.97,1.48-1.17Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M55.6,39.73c.69-.09,1.19.19,1.48.83.11,1.07-.36,1.6-1.43,1.58-.75-.26-1.05-.79-.9-1.58.16-.41.44-.69.85-.83Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M71.38,39.73c1.1-.05,1.6.46,1.48,1.55-.26.65-.73.93-1.43.85-.72-.26-1.01-.77-.9-1.53.16-.41.45-.7.85-.87Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M103.36,42.74c.28,0,.55,0,.83.02,2.9.37,5.8.76,8.69,1.17,1.14.43,1.61,1.25,1.43,2.45-.36,1.01-1.08,1.53-2.16,1.55-2.95-.37-5.89-.76-8.83-1.14-1.35-.44-1.86-1.35-1.53-2.74.33-.68.85-1.12,1.58-1.31Z"
                                        fill="#fdfdfd"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M105.6,48.71c.77-.03,1.48.16,2.14.56,1.03.7,1.89,1.57,2.6,2.6,1.44,2.18,2.58,4.51,3.45,6.99.51,1.49.98,3,1.38,4.51-1.76,1.45-3.78,2.26-6.07,2.45-3.98.14-7.17-1.35-9.59-4.49-.36-.52-.68-1.08-.97-1.65.8-2.72,1.93-5.29,3.4-7.72.5-.78,1.07-1.5,1.72-2.16.56-.53,1.21-.89,1.94-1.09Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M48.95,49.87c.55,0,1.1,0,1.65.02,1.75,1.37,3.72,1.87,5.92,1.5.46-.12.88-.31,1.26-.58,4.06-.03,8.12-.03,12.18,0,.52.39,1.1.62,1.75.68,1.66.14,3.21-.2,4.66-1.02.28-.17.53-.36.78-.58.52-.02,1.03-.03,1.55-.02-.09,1.5-.48,2.9-1.19,4.22-.62,2.83-1.46,5.6-2.52,8.3-.2.41-.41.82-.63,1.21-.76-.1-1.48.04-2.16.41-.31.19-.6.4-.87.63-.83.87-1.66,1.73-2.52,2.57-.28.23-.58.42-.92.56-.21-.14-.41-.31-.58-.51-.8-.47-1.66-.69-2.6-.66-1.14.03-2.25.23-3.33.61-.29.12-.56.25-.83.41-1.09-1.47-2.45-2.61-4.08-3.42-.96-.41-1.96-.59-3.01-.53-.3-.48-.56-.97-.8-1.48-1.02-2.64-1.84-5.34-2.48-8.11-.69-1.33-1.11-2.73-1.24-4.22Z"
                                        fill="#2998e9"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M56.08,52.16h15.63c.1,3.78-1.57,6.45-5,7.99-3.43,1.14-6.36.38-8.81-2.26-1.34-1.67-1.95-3.58-1.82-5.73Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M57.44,53.52h12.82c-.34,2.61-1.73,4.42-4.17,5.41-2.78.86-5.16.23-7.16-1.87-.87-1.02-1.36-2.2-1.48-3.54Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M108.07,57.98c.73-.04,1.2.28,1.43.97.07.73-.25,1.2-.95,1.43-.78.06-1.25-.28-1.43-1.04-.02-.68.3-1.14.95-1.36Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M97.93,61.43c2.16,3.27,5.21,5.17,9.13,5.7,3.08.26,5.88-.5,8.4-2.26,1.31,5.5,1.83,11.09,1.58,16.75-.43,4.08-1.4,8.03-2.91,11.84-1.9,4.73-4.25,9.21-7.04,13.45-.02.04-.03.09-.02.15,2.96.22,5.6,1.25,7.91,3.08,2.18,1.83,3.39,4.17,3.64,7.01-.91.1-1.82.04-2.72-.17-2.26-.54-4.51-1.13-6.75-1.75-1.06-.25-2.14-.42-3.23-.51-.95.04-1.87.18-2.79.41-2.31.61-4.63,1.2-6.94,1.8-.49.09-.97.17-1.46.24-.48.04-.96.03-1.43-.02.05-1.6.51-3.07,1.36-4.42,1.47-2.19,3.43-3.77,5.9-4.73.72-.26,1.45-.49,2.18-.68.02-.02.04-.04.05-.07-3.76-5.59-6.28-11.71-7.55-18.35-.46-2.83-.61-5.68-.44-8.54.33-6.44,1.37-12.75,3.13-18.93Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M117.1,65.84c1.84.71,3.6,1.58,5.29,2.6.69.4,1.3.91,1.82,1.53.56,1.06.89,2.19.97,3.4.07,1.36,0,2.72-.19,4.08-.41,2.46-1,4.89-1.75,7.28-.77,2.41-1.54,4.82-2.31,7.23-.27.02-.53-.02-.78-.12-1.2-.58-2.27-1.33-3.23-2.26.18-.88.39-1.75.63-2.62.85-3.74,1.13-7.53.83-11.36-.18-3.29-.62-6.54-1.29-9.76Z"
                                        fill="#fefefe"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M74.34,66.33h.24c.19,1.79.56,3.53,1.09,5.24.11.25.22.5.32.75-.36.23-.74.44-1.14.61-.17-.24-.3-.5-.39-.78-.63-1.84-1-3.73-1.14-5.66.34-.05.68-.11,1.02-.17Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M53.32,66.43c.44.04.87.09,1.31.15-.18,1.61-.48,3.19-.9,4.76-.21.64-.46,1.25-.75,1.84-.4-.18-.79-.4-1.17-.63.42-.98.76-1.98,1-3.01.2-1.03.37-2.07.51-3.11Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M94.09,72.59s.05.1.05.17c-.44,2.97-.69,5.96-.75,8.96-1.2.85-2.49,1.55-3.86,2.11-.23.09-.48.15-.73.17-.14-1.48.05-2.92.56-4.32.83-2.16,2.02-4.1,3.54-5.83.39-.43.79-.85,1.19-1.26Z"
                                        fill="#fdfdfd"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M47.25,75.84h1.31c-.01.11,0,.2.05.29.07,1.56.51,3,1.33,4.32,1.4,2.09,3.23,3.67,5.51,4.73,4.67,2.1,9.46,2.42,14.37.97,2.59-.78,4.83-2.11,6.72-4,1.37-1.45,2.23-3.16,2.57-5.15.04-.39.07-.78.07-1.17h1.36c-.09,2.63-1,4.93-2.74,6.89-2.24,2.39-4.95,4.01-8.13,4.88-4.65,1.22-9.21.98-13.69-.73-2.73-1.09-4.99-2.79-6.77-5.12-1.26-1.77-1.92-3.74-1.97-5.92Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M42.78,76.62s.09,0,.12.05c3.03,8.57,6.04,17.15,9.03,25.73.06,1.62-.66,2.74-2.16,3.37-1.72.65-3.31.43-4.76-.68-.38-.33-.66-.72-.85-1.19-2.97-8.44-5.93-16.88-8.91-25.31.02-.04.05-.08.1-.1,2.49-.59,4.97-1.21,7.43-1.87Z"
                                        fill="#2998e9"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M84.92,76.62c1.28.33,2.55.66,3.83.97-.54,1.17-.93,2.38-1.19,3.64-.23,1.22-.22,2.45.02,3.66.28.32.63.48,1.07.46.57-.04,1.12-.17,1.65-.39.01.02.03.05.05.07-2.3,6.42-4.6,12.83-6.92,19.25-.78,1.11-1.85,1.72-3.23,1.82-1.5.11-2.75-.38-3.76-1.48-.56-.74-.74-1.57-.53-2.48,2.99-8.52,5.99-17.03,9-25.53Z"
                                        fill="#2998e9"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M51.57,97.25c8.22-.03,16.42,0,24.61.1-.56,1.55-1.1,3.1-1.63,4.66-.25,1.9.4,3.39,1.97,4.49,1.5.93,3.13,1.19,4.85.78,1.23-.34,2.25-1.01,3.03-2.01.2-.29.36-.59.49-.92.85-2.36,1.68-4.72,2.5-7.09h.34c1.03,11.84,2.05,23.69,3.06,35.53v.24h-53.88v-.24c1-11.84,2.02-23.69,3.06-35.53.16-.01.31,0,.46.05.84,2.39,1.68,4.79,2.52,7.18.53,1.13,1.36,1.95,2.5,2.45,1.63.67,3.26.68,4.9.05,2.14-.96,3.1-2.6,2.89-4.93-.53-1.61-1.09-3.21-1.67-4.81Z"
                                        fill="#2998e9"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M47.59,100.16c1.54-.14,2.53.52,2.99,1.99.13,1.48-.51,2.45-1.92,2.89-1.13.17-2-.21-2.65-1.14-.64-1.3-.41-2.41.7-3.33.28-.18.57-.32.87-.41Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M79.14,100.16c1.43-.15,2.4.45,2.89,1.8.26,1.42-.27,2.41-1.58,2.99-1.51.37-2.57-.16-3.18-1.58-.31-1.63.31-2.69,1.87-3.2Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M52.01,106.13h23.69c0,6.7,0,13.4-.02,20.1-.32,2.21-1.54,3.66-3.66,4.34-.28.04-.55.09-.83.15-4.92.03-9.84.03-14.76,0-2.51-.47-3.98-1.97-4.39-4.49-.02-6.7-.03-13.4-.02-20.1Z"
                                        fill="#052350"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                    <path
                                        d="M74.34,107.49c0,6.25,0,12.49-.02,18.74-.33,1.73-1.35,2.78-3.08,3.13-4.94.03-9.87.03-14.81,0-1.9-.43-2.92-1.62-3.06-3.57v-18.3h20.97Z"
                                        fill="#2998e9"
                                        fillRule="evenodd"
                                        strokeWidth="0px"
                                    />
                                </svg>
                            </div>
                            <p className="">
                                Holy mackerel! You are up to date with all your
                                maintenance. Only thing left to do is, to go
                                fishing
                            </p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}
