'use client'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'

import dayjs from 'dayjs'
import React, { useEffect, useState, useCallback } from 'react'
import { debounce, isEmpty, trim } from 'lodash'
import {
    CreateEventType_Tasking,
    UpdateEventType_Tasking,
    CREATE_GEO_LOCATION,
    UPDATE_FUELLOG,
    CREATE_FUELLOG,
    UpdateFuelTank,
} from '@/app/lib/graphQL/mutation'
import {
    GET_FUELLOGS,
    GET_FUELTANKS,
    GetTripEvent,
} from '@/app/lib/graphQL/query'
// Combobox is already imported from '@/components/ui/comboBox'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useToast } from '@/hooks/use-toast'

import LocationField from '../components/location'
import VesselRescueFields from './vessel-rescue-fields'
import Person<PERSON>escueField from './person-rescue-field'
import Editor from '../../editor'
import TimeField from '../components/time'
import {
    Sheet,
    She<PERSON><PERSON>ontent,
    SheetHeader,
    SheetClose,
} from '@/components/ui/sheet'
import RiskAnalysis from './risk-analysis'
import { X, Check, AlertTriangle, SquareArrowOutUpRight } from 'lucide-react'
import { useSearchParams } from 'next/navigation'
import { SealogsFuelIcon } from '@/app/lib/icons/SealogsFuelIcon'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import FuelTankModel from '@/app/offline/models/fuelTank'
import TripEventModel from '@/app/offline/models/tripEvent'
import EventType_TaskingModel from '@/app/offline/models/eventType_Tasking'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import FuelLogModel from '@/app/offline/models/fuelLog'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Combobox } from '@/components/ui/comboBox'
import { Checkbox } from '@/components/ui/checkbox'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import { H3, H4, P } from '@/components/ui/typography'
import { Button } from '@/components/ui'

export default function Tasking({
    geoLocations,
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    type,
    logBookConfig,
    previousDropEvent,
    vessel,
    members,
    locked,
    offline = false,
    fuelLogs,
}: {
    geoLocations: any
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    type: any
    logBookConfig: any
    previousDropEvent: any
    vessel: any
    members: any
    locked: any
    offline?: boolean
    fuelLogs?: any
}) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const { toast } = useToast()
    const [locations, setLocations] = useState<any>(false)
    const [time, setTime] = useState<any>(dayjs().format('HH:mm'))
    const [tasking, setTasking] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [parentLocation, setParentLocation] = useState<any>(false)
    const [openRiskAnalysis, setOpenRiskAnalysis] = useState<any>(false)
    const [pauseGroup, setPauseGroup] = useState<any>(false)
    const [openTaskID, setOpenTaskID] = useState<any>(false)
    const [completedTaskID, setCompletedTaskID] = useState<any>(false)
    const [towingChecklistID, setTowingChecklistID] = useState<any>(0)
    const [groupID, setGroupID] = useState<any>(false)
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [currentIncident, setCurrentIncident] = useState<any>(false)
    const [content, setContent] = useState<any>('')
    const [taskingPausedValue, setTaskingPausedValue] = useState<any>(null)
    const [taskingResumedValue, setTaskingResumedValue] = useState<any>(null)
    const [taskingCompleteValue, setTaskingCompleteValue] = useState<any>(null)
    const [locationDescription, setLocationDescription] = useState('')
    const [allChecked, setAllChecked] = useState<any>(false)
    // const [members, setMembers] = useState<any>(false)
    const [logbook, setLogbook] = useState<any>(false)
    const [fuelTankList, setFuelTankList] = useState<any>(false)
    const [fuelTankBuffer, setFuelTankBuffer] = useState<any>([])
    const [updatedFuelLogs, setUpdatedFuelLogs] = useState<any>([])
    const [location, setLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })

    const [openNewLocationDialog, setOpenNewLocationDialog] =
        useState<boolean>(false)

    const [permissions, setPermissions] = useState<any>(false)
    const [editTaskingRisk, setEditTaskingRisk] = useState<any>(false)
    const fuelTankModel = new FuelTankModel()
    const tripEventModel = new TripEventModel()
    const taskingModel = new EventType_TaskingModel()
    const geoLocationModel = new GeoLocationModel()
    const fuelLogModel = new FuelLogModel()
    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_LOGBOOKENTRY_RISK_ANALYSIS', permissions)) {
                setEditTaskingRisk(true)
            } else {
                setEditTaskingRisk(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const [queryGetFuelTanks] = useLazyQuery(GET_FUELTANKS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readFuelTanks.nodes
            // Initialize currentLevel for each tank if not already set
            const initializedData = data.map((tank: any) => ({
                ...tank,
                currentLevel: tank.currentLevel ?? getInitialFuelLevel(tank),
            }))
            setFuelTankList(initializedData)
        },
        onError: (error: any) => {
            console.error('getFuelTanks error', error)
        },
    })

    const getFuelTanks = async (fuelTankIds: any) => {
        if (offline) {
            const data = await fuelTankModel.getByIds(fuelTankIds)
            // Initialize currentLevel for each tank if not already set
            const initializedData = data.map((tank: any) => ({
                ...tank,
                currentLevel: tank.currentLevel ?? getInitialFuelLevel(tank),
            }))
            setFuelTankList(initializedData)
        } else {
            await queryGetFuelTanks({
                variables: {
                    id: fuelTankIds,
                },
            })
        }
    }

    const handleSetVessel = (vessel: any) => {
        const fuelTankIds = vessel?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'FuelTank',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        fuelTankIds.length > 0 && getFuelTanks(fuelTankIds)
    }

    useEffect(() => {
        if (vessel) {
            handleSetVessel(vessel)
        }
    }, [vessel])

    const handleTimeChange = (date: any) => {
        setTime(dayjs(date).format('HH:mm'))
    }

    const offlineGetPreviousDropEvent = async () => {
        const event = await tripEventModel.getById(previousDropEvent?.id)
        if (event) {
            setGroupID(event.eventType_Tasking?.groupID)
            if (event.eventType_Tasking?.lat && event.eventType_Tasking?.long) {
                setCurrentLocation({
                    latitude: event.eventType_Tasking?.lat,
                    longitude: event.eventType_Tasking?.long,
                })
            }
        }
    }
    useEffect(() => {
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        } else {
            if (previousDropEvent?.id > 0) {
                if (offline) {
                    offlineGetPreviousDropEvent()
                } else {
                    getPreviousDropEvent({
                        variables: {
                            id: previousDropEvent?.id,
                        },
                    })
                }
            }
        }
    }, [selectedEvent])

    useEffect(() => {
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    const handleTaskingPauseChange = (selectedTask: any) => {
        setPauseGroup(selectedTask.value)
        setTaskingPausedValue(selectedTask)
    }

    const [getPreviousDropEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setGroupID(event.eventType_Tasking?.groupID)
                if (
                    event.eventType_Tasking?.lat &&
                    event.eventType_Tasking?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_Tasking?.lat,
                        longitude: event.eventType_Tasking?.long,
                    })
                }
            }
        },
        onError: (error) => {
            console.error('Error getting previous event', error)
        },
    })

    const getCurrentEvent = async (id: any) => {
        if (offline) {
            const event = await tripEventModel.getById(id)
            if (event) {
                // eventType_TaskingID
                if (!event.eventType_Tasking) {
                    const eventType_Tasking = await taskingModel.getById(
                        event.eventType_TaskingID,
                    )
                    event.eventType_Tasking = eventType_Tasking
                }
                setTripEvent(event)

                setTasking({
                    geoLocationID: event.eventType_Tasking?.geoLocationID
                        ? event.eventType_Tasking?.geoLocationID
                        : null,
                    time: event.eventType_Tasking?.time,
                    title: event.eventType_Tasking?.title
                        ? event.eventType_Tasking?.title
                        : '',
                    fuelLevel: event.eventType_Tasking?.fuelLevel
                        ? event.eventType_Tasking?.fuelLevel
                        : '',
                    type: event.eventType_Tasking?.type
                        ? event.eventType_Tasking?.type
                        : '',
                    operationType:
                        event.eventType_Tasking?.operationType?.replaceAll(
                            '_',
                            ' ',
                        ),
                    lat: event.eventType_Tasking?.lat
                        ? event.eventType_Tasking?.lat
                        : '',
                    long: event.eventType_Tasking?.long
                        ? event.eventType_Tasking?.long
                        : '',
                    vesselRescueID: event.eventType_Tasking?.vesselRescueID
                        ? event.eventType_Tasking?.vesselRescueID
                        : 0,
                    personRescueID: event.eventType_Tasking?.personRescueID
                        ? event.eventType_Tasking?.personRescueID
                        : 0,
                    groupID: event.eventType_Tasking?.groupID
                        ? event.eventType_Tasking?.groupID
                        : null,
                    comments: event.eventType_Tasking?.comments
                        ? event.eventType_Tasking?.comments
                        : '',
                    tripEventID: event.eventType_Tasking?.id
                        ? event.eventType_Tasking?.id
                        : null,
                    pausedTaskID: event.eventType_Tasking?.pausedTaskID
                        ? event.eventType_Tasking?.pausedTaskID
                        : null,
                    openTaskID: event.eventType_Tasking?.openTaskID
                        ? event.eventType_Tasking?.openTaskID
                        : null,
                    completedTaskID: event.eventType_Tasking?.completedTaskID
                        ? event.eventType_Tasking?.completedTaskID
                        : null,
                    status: event.eventType_Tasking?.status,
                    cgop: event.eventType_Tasking?.cgop
                        ? event.eventType_Tasking?.cgop
                        : '',
                    sarop: event.eventType_Tasking?.sarop
                        ? event.eventType_Tasking?.sarop
                        : '',
                    fuelLog: event.eventType_Tasking?.fuelLog?.nodes,
                })
                setGroupID(event?.eventType_Tasking?.groupID)
                setContent(event?.eventType_Tasking?.comments)
                setTime(event.eventType_Tasking?.time)
                setCompletedTaskID(
                    event.eventType_Tasking?.completedTaskID
                        ? event.eventType_Tasking.completedTaskID
                        : null,
                )
                setOpenTaskID(
                    event.eventType_Tasking?.openTaskID
                        ? event.eventType_Tasking?.openTaskID
                        : null,
                )
                setPauseGroup(
                    event.eventType_Tasking?.pausedTaskID
                        ? event.eventType_Tasking?.pausedTaskID
                        : null,
                )
                if (
                    event.eventType_Tasking?.lat &&
                    event.eventType_Tasking?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_Tasking?.lat,
                        longitude: event.eventType_Tasking?.long,
                    })
                }
            }
            const resumedEvent = currentTrip?.tripEvents?.nodes.filter(
                (event: any) =>
                    event?.eventCategory === 'Tasking' &&
                    event?.id !== currentEvent?.id &&
                    event?.eventType_Tasking?.type === 'TaskingResumed',
            )
            if (resumedEvent) {
                setGroupID(resumedEvent[0]?.eventType_Tasking?.groupID)
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTripEvent(event)

                setTasking({
                    geoLocationID: event.eventType_Tasking?.geoLocationID
                        ? event.eventType_Tasking?.geoLocationID
                        : null,
                    time: event.eventType_Tasking?.time,
                    title: event.eventType_Tasking?.title
                        ? event.eventType_Tasking?.title
                        : '',
                    fuelLevel: event.eventType_Tasking?.fuelLevel
                        ? event.eventType_Tasking?.fuelLevel
                        : '',
                    type: event.eventType_Tasking?.type
                        ? event.eventType_Tasking?.type
                        : '',
                    operationType:
                        event.eventType_Tasking?.operationType?.replaceAll(
                            '_',
                            ' ',
                        ),
                    lat: event.eventType_Tasking?.lat
                        ? event.eventType_Tasking?.lat
                        : '',
                    long: event.eventType_Tasking?.long
                        ? event.eventType_Tasking?.long
                        : '',
                    vesselRescueID: event.eventType_Tasking?.vesselRescueID
                        ? event.eventType_Tasking?.vesselRescueID
                        : 0,
                    personRescueID: event.eventType_Tasking?.personRescueID
                        ? event.eventType_Tasking?.personRescueID
                        : 0,
                    groupID: event.eventType_Tasking?.groupID
                        ? event.eventType_Tasking?.groupID
                        : null,
                    comments: event.eventType_Tasking?.comments
                        ? event.eventType_Tasking?.comments
                        : '',
                    tripEventID: event.eventType_Tasking?.id
                        ? event.eventType_Tasking?.id
                        : null,
                    pausedTaskID: event.eventType_Tasking?.pausedTaskID
                        ? event.eventType_Tasking?.pausedTaskID
                        : null,
                    openTaskID: event.eventType_Tasking?.openTaskID
                        ? event.eventType_Tasking?.openTaskID
                        : null,
                    completedTaskID: event.eventType_Tasking?.completedTaskID
                        ? event.eventType_Tasking?.completedTaskID
                        : null,
                    status: event.eventType_Tasking?.status,
                    cgop: event.eventType_Tasking?.cgop
                        ? event.eventType_Tasking?.cgop
                        : '',
                    sarop: event.eventType_Tasking?.sarop
                        ? event.eventType_Tasking?.sarop
                        : '',
                    fuelLog: event.eventType_Tasking?.fuelLog?.nodes,
                })
                setGroupID(event?.eventType_Tasking?.groupID)
                setContent(event?.eventType_Tasking?.comments)
                setTime(event.eventType_Tasking?.time)
                setCompletedTaskID(
                    event.eventType_Tasking?.completedTaskID
                        ? event.eventType_Tasking.completedTaskID
                        : null,
                )
                setOpenTaskID(
                    event.eventType_Tasking?.openTaskID
                        ? event.eventType_Tasking?.openTaskID
                        : null,
                )
                setPauseGroup(
                    event.eventType_Tasking?.pausedTaskID
                        ? event.eventType_Tasking?.pausedTaskID
                        : null,
                )
                if (
                    event.eventType_Tasking?.lat &&
                    event.eventType_Tasking?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_Tasking?.lat,
                        longitude: event.eventType_Tasking?.long,
                    })
                }
            }
            const resumedEvent = currentTrip?.tripEvents?.nodes.filter(
                (event: any) =>
                    event?.eventCategory === 'Tasking' &&
                    event?.id !== currentEvent?.id &&
                    event?.eventType_Tasking?.type === 'TaskingResumed',
            )
            if (resumedEvent) {
                setGroupID(resumedEvent[0]?.eventType_Tasking?.groupID)
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    useEffect(() => {
        if (geoLocations) {
            setLocations([
                { label: '--- Add new location ---', value: 'newLocation' },
                ...geoLocations
                    .filter((location: any) => location.title)
                    .map((location: any) => ({
                        label: location.title,
                        value: location.id,
                        latitude: location.lat,
                        longitude: location.long,
                    })),
            ])
        }
    }, [geoLocations])

    const handleSave = async (vesselRescueID = 0, personRescueID = 0) => {
        const variables = {
            input: {
                geoLocationID: tasking?.geoLocationID,
                time: time,
                title: tasking?.title,
                fuelLevel: tasking?.fuelLevel,
                type: type,
                operationType: tasking?.operationType,
                lat: currentLocation.latitude.toString(),
                long: currentLocation.longitude.toString(),
                vesselRescueID:
                    vesselRescueID > 0
                        ? vesselRescueID
                        : tasking?.vesselRescueID,
                personRescueID:
                    personRescueID > 0
                        ? personRescueID
                        : tasking?.personRescueID,
                currentEntryID: currentTrip.id,
                tripEventID: tasking?.id,
                pausedTaskID: +pauseGroup,
                openTaskID: +openTaskID,
                completedTaskID: +completedTaskID,
                comments: content,
                groupID: +groupID,
                status: 'Open',
                cgop: tasking?.cgop ? tasking.cgop : null,
                sarop: tasking?.sarop ? tasking.sarop : null,
            },
        }

        if (pauseGroup > 0) {
            if (offline) {
                const x = await taskingModel.save({
                    id: +pauseGroup,
                    status: 'Paused',
                    tripEventID: currentEvent?.id,
                })
                updateTripReport(currentTrip)
                updateTripReport({
                    id: tripReport.map((trip: any) => trip.id),
                })
                closeModal()
            } else {
                updateEventType_tasking({
                    variables: {
                        input: {
                            id: +pauseGroup,
                            status: 'Paused',
                            tripEventID: currentEvent?.id,
                        },
                    },
                })
            }
        }
        if (openTaskID > 0) {
            if (offline) {
                const x = await taskingModel.save({
                    id: +openTaskID,
                    status: 'Open',
                    tripEventID: currentEvent?.id,
                })
                updateTripReport(currentTrip)
                updateTripReport({
                    id: tripReport.map((trip: any) => trip.id),
                })
                closeModal()
            } else {
                updateEventType_tasking({
                    variables: {
                        input: {
                            id: +openTaskID,
                            status: 'Open',
                            tripEventID: currentEvent?.id,
                        },
                    },
                })
            }
        }
        if (completedTaskID > 0 && !currentEvent) {
            if (offline) {
                const x = await taskingModel.save({
                    id:
                        +completedTaskID > 0
                            ? +completedTaskID
                            : generateUniqueId(),
                    status: 'Completed',
                    tripEventID: currentEvent?.id,
                })
                updateTripReport(currentTrip)
                updateTripReport({
                    id: tripReport.map((trip: any) => trip.id),
                })
                closeModal()
            } else {
                updateEventType_tasking({
                    variables: {
                        input: {
                            id: +completedTaskID,
                            status: 'Completed',
                            tripEventID: currentEvent?.id,
                        },
                    },
                })
            }
        }
        if (currentEvent) {
            if (offline) {
                const data = await taskingModel.save({
                    ...variables.input,
                    id: +selectedEvent?.eventType_TaskingID,
                    tripEventID: +selectedEvent?.id,
                })
                updateTripReport(currentTrip)
                updateTripReport({
                    id: tripReport.map((trip: any) => trip.id),
                })
                closeModal()
                updateFuelLogs(+selectedEvent?.eventType_TaskingID)
                await getCurrentEvent(+selectedEvent?.eventType_TaskingID)
            } else {
                updateEventType_tasking({
                    variables: {
                        input: {
                            id: +selectedEvent?.eventType_TaskingID,
                            ...variables.input,
                        },
                    },
                })
                updateFuelLogs(+selectedEvent?.eventType_TaskingID)
            }
        } else {
            if (offline) {
                const newID = generateUniqueId()
                await tripEventModel.save({
                    id: newID,
                    eventCategory: 'Tasking',
                    eventType_TaskingID: +newID,
                    logBookEntrySectionID: currentTrip.id,
                })
                const data = await taskingModel.save({
                    id: +newID,
                    geoLocationID: tasking?.geoLocationID,
                    time: time,
                    title: tasking?.title,
                    fuelLevel: tasking?.fuelLevel,
                    type: type,
                    operationType: tasking?.operationType,
                    lat: currentLocation.latitude.toString(),
                    long: currentLocation.longitude.toString(),
                    vesselRescueID: vesselRescueID,
                    personRescueID: personRescueID,
                    currentEntryID: currentTrip.id,
                    pausedTaskID: +pauseGroup,
                    openTaskID: +openTaskID,
                    completedTaskID: +completedTaskID,
                    comments: content,
                    groupID: +groupID,
                    status: 'Open',
                    cgop: tasking?.cgop ? tasking.cgop : getPreviousCGOP(false),
                    sarop: tasking?.sarop
                        ? tasking.sarop
                        : getPreviousSAROP(false),
                    tripEventID: newID,
                })
                updateFuelLogs(+data.id)
                updateTripReport(currentTrip)
                updateTripReport({
                    id: tripReport.map((trip: any) => trip.id),
                })
            } else {
                createEventType_Tasking({
                    variables: {
                        input: {
                            geoLocationID: tasking?.geoLocationID,
                            time: time,
                            title: tasking?.title,
                            fuelLevel: tasking?.fuelLevel,
                            type: type,
                            operationType: tasking?.operationType,
                            lat: currentLocation.latitude.toString(),
                            long: currentLocation.longitude.toString(),
                            vesselRescueID: vesselRescueID,
                            personRescueID: personRescueID,
                            currentEntryID: currentTrip.id,
                            pausedTaskID: +pauseGroup,
                            openTaskID: +openTaskID,
                            completedTaskID: +completedTaskID,
                            comments: content,
                            groupID: +groupID,
                            status: 'Open',
                            cgop: tasking?.cgop
                                ? tasking.cgop
                                : getPreviousCGOP(false),
                            sarop: tasking?.sarop
                                ? tasking.sarop
                                : getPreviousSAROP(false),
                        },
                    },
                })
            }
        }
        setCompletedTaskID(false)
        setOpenTaskID(false)
        setPauseGroup(false)
    }

    const [createEventType_Tasking] = useMutation(CreateEventType_Tasking, {
        onCompleted: (response) => {
            const data = response.createEventType_Tasking
            updateFuelLogs(+data.id)
            updateTripReport(currentTrip)
            updateTripReport({
                id: tripReport.map((trip: any) => trip.id),
            })
            closeModal()
        },
        onError: (error) => {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.message,
            })
        },
    })

    const [updateEventType_tasking] = useMutation(UpdateEventType_Tasking, {
        onCompleted: (response) => {
            const data = response.updateEventType_tasking
            updateTripReport(currentTrip)
            updateTripReport({
                id: tripReport.map((trip: any) => trip.id),
            })
            closeModal()
        },
        onError: (error) => {
            console.error('Error updating activity type tasking', error)
        },
    })

    const handleOperationTypeChange = (selectedOperation: any) => {
        if (selectedOperation.value === 'newLocation') {
            toast({
                title: 'Getting your current location...',
                description: 'Please wait...',
            })
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(({ coords }) => {
                    const { latitude, longitude } = coords
                    setLocation({ latitude, longitude })
                    toast({
                        title: 'Success',
                        description: 'Location found',
                    })
                    setOpenNewLocationDialog(true)
                })
            } else {
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Geolocation is not supported by your browser',
                })
                setOpenNewLocationDialog(true)
            }
        } else {
            setTasking({
                ...tasking,
                operationType: selectedOperation.value,
            })
        }
    }

    const handleLocationChange = (value: any) => {
        // If value is null or undefined, clear the location
        if (!value) {
            setTasking({
                ...tasking,
                geoLocationID: 0,
                lat: null,
                long: null,
            })
            return
        }

        // Handle "Add new location" option
        if (value.value === 'newLocation') {
            toast({
                title: 'Getting your current location...',
                description: 'Please wait...',
            })
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(({ coords }) => {
                    const { latitude, longitude } = coords
                    setLocation({ latitude, longitude })
                    setOpenNewLocationDialog(true)
                })
            } else {
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Geolocation is not supported by your browser',
                })
                setOpenNewLocationDialog(true)
            }
            return
        }

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setTasking({
                ...tasking,
                geoLocationID: +value.value,
                lat: null,
                long: null,
            })

            // If the value object has latitude and longitude, update currentLocation
            if (value.latitude !== undefined && value.longitude !== undefined) {
                setCurrentLocation({
                    latitude: value.latitude,
                    longitude: value.longitude,
                })
            }
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setTasking({
                ...tasking,
                geoLocationID: 0, // Reset geoLocationID when using direct coordinates
                lat: value.latitude,
                long: value.longitude,
            })

            // Update currentLocation
            setCurrentLocation({
                latitude: value.latitude,
                longitude: value.longitude,
            })
        }
    }

    const handleCreateNewLocation = async () => {
        const title = document.getElementById(
            'new-location-title',
        ) as HTMLInputElement
        const latitude = document.getElementById(
            'new-location-latitude',
        ) as HTMLInputElement
        const longitude = document.getElementById(
            'new-location-longitude',
        ) as HTMLInputElement
        if (title && latitude && longitude) {
            if (offline) {
                const data = await geoLocationModel.save({
                    id: generateUniqueId(),
                    title: title.value,
                    lat: +latitude.value,
                    long: +longitude.value,
                    parentID: parentLocation,
                })
                setLocations([
                    ...locations,
                    {
                        label: data.title,
                        value: data.id,
                        latitude: data.lat,
                        longitude: data.long,
                    },
                ])
                setTasking({
                    ...tasking,
                    geoLocationID: data.id,
                })
                setOpenNewLocationDialog(false)
            } else {
                createGeoLocation({
                    variables: {
                        input: {
                            title: title.value,
                            lat: +latitude.value,
                            long: +longitude.value,
                            parentID: parentLocation,
                        },
                    },
                })
            }
        }
    }

    const [createGeoLocation] = useMutation(CREATE_GEO_LOCATION, {
        onCompleted: (response) => {
            const data = response.createGeoLocation
            setLocations([
                ...locations,
                {
                    label: data.title,
                    value: data.id,
                    latitude: data.lat,
                    longitude: data.long,
                },
            ])
            setTasking({
                ...tasking,
                geoLocationID: data.id,
            })
            setOpenNewLocationDialog(false)
        },
        onError: (error) => {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Error creating GeoLocation',
            })
            console.error('Error creating GeoLocation: ' + error.message)
            setOpenNewLocationDialog(false)
            console.error('Error creating new location', error)
        },
    })

    const handleParentLocationChange = (selectedOption: any) => {
        if (selectedOption) {
            setParentLocation(selectedOption.value)
        } else {
            setParentLocation(null)
        }
    }

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleTaskingGroupChange = (selectedGroup: any) => {
        setGroupID(selectedGroup.value)
        setOpenTaskID(selectedGroup.value)
        setTaskingResumedValue(selectedGroup)
    }

    const handleTaskingCompleteChange = (selectedGroup: any) => {
        setCompletedTaskID(selectedGroup.value)
        setTaskingCompleteValue(selectedGroup)
    }

    const operationTypes = [
        {
            label: 'Vessel Mechanical / equipment failure',
            value: 'Vessel Mechanical or equipment failure',
        },
        { label: 'Vessel adrift', value: 'Vessel adrift' },
        { label: 'Vessel aground', value: 'Vessel aground' },
        { label: 'Capsize', value: 'Capsize' },
        { label: 'Vessel requiring tow', value: 'Vessel requiring tow' },
        { label: 'Flare sighting', value: 'Flare sighting' },
        { label: 'Vessel sinking', value: 'Vessel sinking' },
        { label: 'Collision', value: 'Collision' },
        { label: 'Vessel overdue', value: 'Vessel overdue' },
        { label: 'Vessel - other', value: 'Vessel - other' },
        { label: 'Person in water', value: 'Person in water' },
        { label: 'Person lost / missing', value: 'Person lost or missing' },
        { label: 'Suicide', value: 'Suicide' },
        { label: 'Medical condition', value: 'Medical condition' },
        { label: 'Person - other', value: 'Person - other' },
    ]

    const goSetTaskingTitle = (event: any) => {
        let title = ''
        if (event && event.eventType_Tasking.type === 'TaskingStartUnderway') {
            title = event.eventType_Tasking.title
        }

        if (isEmpty(trim(tasking.title)) && !isEmpty(trim(title))) {
            setTasking({
                ...tasking,
                title: title,
            })
        }
    }

    const findPreviousEvent = (selectedEvent: any) => {
        const prevEvent = previousDropEvent
            ?.filter((event: any) => event.eventType_Tasking.status === 'Open')
            .pop()
        if (type === 'TaskingOnTow' || type === 'TaskingOnScene') {
            if (selectedEvent) {
                if (selectedEvent.eventType_Tasking.vesselRescueID > 0) {
                    const res = previousDropEvent
                        .filter(
                            (event: any) =>
                                event.eventType_Tasking.vesselRescueID ===
                                selectedEvent.eventType_Tasking.vesselRescueID,
                        )
                        .pop()
                    goSetTaskingTitle(res)
                    return res
                }
                if (selectedEvent.eventType_Tasking.personRescueID > 0) {
                    const res = previousDropEvent
                        .filter(
                            (event: any) =>
                                event.eventType_Tasking.personRescueID ===
                                selectedEvent.eventType_Tasking.personRescueID,
                        )
                        .pop()
                    goSetTaskingTitle(res)
                    return res
                }
            }
            goSetTaskingTitle(prevEvent)
            return prevEvent
        }
        if (type === 'TaskingComplete') {
            if (completedTaskID > 0) {
                const res = currentTrip?.tripEvents?.nodes.find(
                    (event: any) =>
                        event?.eventType_TaskingID == completedTaskID,
                )
                goSetTaskingTitle(res)
                return res
            }
            if (tasking.completedTaskID > 0) {
                const res = currentTrip?.tripEvents?.nodes.find(
                    (event: any) =>
                        event?.eventType_TaskingID == tasking.completedTaskID,
                )
                goSetTaskingTitle(res)
                return res
            } else {
                const res = prevEvent ? prevEvent : selectedEvent
                goSetTaskingTitle(res)
                return res
            }
        }
        goSetTaskingTitle(selectedEvent)
        return selectedEvent
    }

    const findPreviousRescueID = (rescueID: any) => {
        const prevEvent = previousDropEvent
            ?.filter((event: any) => event.eventType_Tasking.status === 'Open')
            .pop()
        if (type === 'TaskingOnTow' || type === 'TaskingOnScene') {
            return prevEvent
                ? prevEvent.eventType_Tasking.vesselRescueID
                : rescueID
        }
        if (type === 'TaskingComplete') {
            if (tasking.completedTaskID > 0) {
                return tasking.vesselRescueID
            }
            if (completedTaskID > 0) {
                return +currentTrip?.tripEvents?.nodes.find(
                    (event: any) =>
                        event?.eventType_TaskingID == completedTaskID,
                )?.eventType_Tasking?.vesselRescueID
            }
            if (tasking.completedTaskID > 0) {
                return +currentTrip?.tripEvents?.nodes.find(
                    (event: any) =>
                        event?.eventType_TaskingID == tasking.completedTaskID,
                )?.eventType_Tasking?.vesselRescueID
            } else {
                return prevEvent
                    ? prevEvent.eventType_Tasking.vesselRescueID
                    : rescueID
            }
        }
        return rescueID
    }

    const findPreviousHumanRescueID = (rescueID: any) => {
        const prevEvent = previousDropEvent
            ?.filter((event: any) => event.eventType_Tasking.status === 'Open')
            .pop()
        if (type === 'TaskingOnTow' || type === 'TaskingOnScene') {
            return prevEvent
                ? prevEvent.eventType_Tasking.personRescueID
                : rescueID
        }
        if (type === 'TaskingComplete') {
            if (tasking.completedTaskID > 0) {
                return tasking.personRescueID
            }
            if (completedTaskID > 0) {
                return +currentTrip?.tripEvents?.nodes.find(
                    (event: any) =>
                        event?.eventType_TaskingID == completedTaskID,
                )?.eventType_Tasking?.personRescueID
            }
            if (tasking.completedTaskID > 0) {
                return +currentTrip?.tripEvents?.nodes.find(
                    (event: any) =>
                        event?.eventType_TaskingID == tasking.completedTaskID,
                )?.eventType_Tasking?.personRescueID
            } else {
                return prevEvent
                    ? prevEvent.eventType_Tasking.personRescueID
                    : rescueID
            }
        }
        return rescueID
    }

    const currentOperationTypeLabel = (label: any) => {
        return label ? label : '-- Select operation type --'
    }

    const currentOperationTypeValue = (value: any) => {
        return value
    }

    const getPreviousSAROP = (sarop: any) => {
        if (currentIncident === 'cgop') {
            return ''
        }
        if (currentIncident === 'sarop') {
            return sarop ? sarop : ' '
        }
        const e = previousDropEvent
            ?.filter((event: any) => event.eventType_Tasking.status === 'Open')
            .pop()
        if (type === 'TaskingComplete') {
            const completedEvent = currentTrip?.tripEvents?.nodes.find(
                (event: any) => event?.eventType_TaskingID == completedTaskID,
            )
        }
        return e?.eventType_Tasking?.sarop
            ? e.eventType_Tasking.sarop
            : sarop
              ? sarop
              : ''
    }

    const getPreviousCGOP = (cgop: any) => {
        if (currentIncident === 'sarop') {
            return ''
        }
        if (currentIncident === 'cgop') {
            return cgop ? cgop : ' '
        }
        const e = previousDropEvent
            ?.filter((event: any) => event.eventType_Tasking.status === 'Open')
            .pop()
        if (type === 'TaskingComplete') {
            const completedEvent = currentTrip?.tripEvents?.nodes.find(
                (event: any) => event?.eventType_TaskingID == completedTaskID,
            )
        }
        return e?.eventType_Tasking?.cgop
            ? e.eventType_Tasking.cgop
            : cgop
              ? cgop
              : ''
    }

    const getIsSAROP = (sarop: any) => {
        return (
            currentIncident === 'sarop' ||
            (currentIncident !== 'sarop' &&
                !isEmpty(trim(sarop)) &&
                isEmpty(trim(tasking?.cgop ?? '')) &&
                !isEmpty(trim(tasking?.sarop ?? '')))
        )
    }

    const getIsCGOP = (cgop: any) => {
        return (
            currentIncident === 'cgop' ||
            (currentIncident !== 'cgop' &&
                !isEmpty(trim(cgop)) &&
                isEmpty(trim(tasking?.sarop ?? '')) &&
                !isEmpty(trim(tasking?.cgop ?? '')))
        )
    }

    const getPreviousFuelLevel = (fuelLevel: any) => {
        if (selectedEvent?.eventType_Tasking?.fuelLevel > 0) {
            return selectedEvent?.eventType_Tasking?.fuelLevel
        }
        if (fuelLevel || tasking?.updatedFuelLevel) {
            return fuelLevel
        }
        const fuelLevels = currentTrip?.tripEvents?.nodes
            .filter((event: any) => event.eventType_Tasking.fuelLevel > 0)
            .map((event: any) => event.eventType_Tasking.fuelLevel)
        const minFuelLevel = fuelLevels?.length
            ? fuelLevels[fuelLevels.length - 1]
            : fuelLevel
        return fuelLevels?.length ? minFuelLevel : fuelLevel ? fuelLevel : ''
    }

    const getPreviousTask = (task: any) => {
        if (task) {
            return task
        }
        const prevEvent = previousDropEvent
            ?.filter((event: any) => event.eventType_Tasking.status === 'Open')
            .pop()
        if (type === 'TaskingComplete') {
            setCompletedTaskID(prevEvent?.eventType_Tasking?.id)
            setTaskingCompleteValue({
                label:
                    prevEvent?.eventType_Tasking?.time +
                    ' - ' +
                    prevEvent?.eventType_Tasking?.title,
                value: prevEvent?.eventType_Tasking?.id,
            })
        }
        return prevEvent
            ? {
                  label:
                      prevEvent?.eventType_Tasking?.time +
                      ' - ' +
                      prevEvent?.eventType_Tasking?.title,
                  value: prevEvent?.eventType_Tasking?.id,
              }
            : task
    }

    const isVesselRescue = () => {
        if (type === 'TaskingComplete' && tasking.completedTaskID > 0) {
            return (
                currentTrip?.tripEvents?.nodes.find(
                    (event: any) =>
                        event?.eventType_TaskingID == tasking.completedTaskID,
                )?.eventType_Tasking?.vesselRescueID > 0
            )
        }
        if (type === 'TaskingOnScene' || type === 'TaskingOnTow') {
            var latestEvent: any
            currentTrip?.tripEvents?.nodes.filter((event: any) => {
                if (event?.eventCategory === 'Tasking') {
                    latestEvent = event
                }
            })
            return latestEvent?.eventType_Tasking?.vesselRescueID > 0
        }
        return (
            currentTrip?.tripEvents?.nodes.find(
                (event: any) => event?.eventType_TaskingID == completedTaskID,
            )?.eventType_Tasking?.vesselRescueID > 0
        )
    }

    const isPersonRescue = () => {
        if (type === 'TaskingComplete' && tasking.completedTaskID > 0) {
            return (
                currentTrip?.tripEvents?.nodes.find(
                    (event: any) =>
                        event?.eventType_TaskingID == tasking.completedTaskID,
                )?.eventType_Tasking?.personRescueID > 0
            )
        }
        if (type === 'TaskingOnScene' || type === 'TaskingOnTow') {
            var latestEvent: any
            currentTrip?.tripEvents?.nodes.filter((event: any) => {
                if (event?.eventCategory === 'Tasking') {
                    latestEvent = event
                }
            })
            return latestEvent?.eventType_Tasking?.personRescueID > 0
        }
        return (
            currentTrip?.tripEvents?.nodes.find(
                (event: any) => event?.eventType_TaskingID == completedTaskID,
            )?.eventType_Tasking?.personRescueID > 0
        )
    }

    const displayVessesRescueFields = () => {
        if (
            (type === 'TaskingOnScene' && isVesselRescue()) ||
            (type === 'TaskingOnTow' && isVesselRescue()) ||
            (type === 'TaskingComplete' && isVesselRescue()) ||
            tasking.operationType ===
                'Vessel Mechanical or equipment failure' ||
            tasking.operationType === 'Vessel adrift' ||
            tasking.operationType === 'Vessel aground' ||
            tasking.operationType === 'Capsize' ||
            tasking.operationType === 'Vessel requiring tow' ||
            tasking.operationType === 'Flare sighting' ||
            tasking.operationType === 'Vessel sinking' ||
            tasking.operationType === 'Collision' ||
            tasking.operationType === 'Vessel overdue' ||
            tasking.operationType === 'Vessel - other'
        ) {
            return true
        }
        return false
    }

    const displayPersonRescueFields = () => {
        if (
            (type === 'TaskingOnScene' && isPersonRescue()) ||
            (type === 'TaskingOnTow' && isPersonRescue()) ||
            (type === 'TaskingComplete' && isPersonRescue()) ||
            tasking.operationType === 'Person in water' ||
            tasking.operationType === 'Person lost or missing' ||
            tasking.operationType === 'Suicide' ||
            tasking.operationType === 'Medical condition' ||
            tasking.operationType === 'Person - other'
        ) {
            return true
        }
        return false
    }

    const handleSaropChange = (e: any) => {
        if (e.target.value == 'on') {
            setCurrentIncident('sarop')
        }
    }

    const handleCgopChange = (e: any) => {
        if (e.target.value == 'on') {
            setCurrentIncident('cgop')
        }
    }

    const handleUpdateFuelTank = (tank: any, value: any) => {
        if (tank.capacity < +value) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description:
                    'Fuel level cannot be higher than tank capacity of ' +
                    tank.capacity,
            })
            return
        }
        setFuelTankList(
            fuelTankList.map((item: any) => {
                if (item.id === tank.id) {
                    return {
                        ...item,
                        currentLevel: +value,
                    }
                }
                return item
            }),
        )
        setTasking({
            ...tasking,
            fuelLog: false,
        })
        if (
            fuelTankBuffer.length > 0 &&
            fuelTankBuffer.filter((item: any) => item.tankID === tank.id)
        ) {
            setFuelTankBuffer(
                fuelTankBuffer.map((item: any) => {
                    if (item.tankID === tank.id) {
                        return {
                            ...item,
                            value: +value,
                        }
                    }
                    return item
                }),
            )
        } else {
            setFuelTankBuffer([
                ...fuelTankBuffer,
                { tankID: tank.id, value: +value },
            ])
        }
    }

    // Create a debounced version of the update function
    // const handleUpdateFuelTank = useCallback(
    //     debounce((tank: any, value: any) => {
    //         updateFuelTankValue(tank, value)
    //     }, 500), // 500ms delay
    //     [fuelTankList, tasking],
    // )

    const [updateFuelLog] = useMutation(UPDATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.updateFuelLog
        },
        onError: (error) => {
            console.error('Error updating fuel log', error)
        },
    })

    const [createFuelLog] = useMutation(CREATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.createFuelLog
        },
        onError: (error) => {
            console.error('Error creating fuel log', error)
        },
    })

    const [updateFuelTank] = useMutation(UpdateFuelTank, {
        onCompleted: (response) => {
            const data = response.updateFuelTank
            const fuelLog = updatedFuelLogs
                .filter((log: any) => log.fuelTank.id === data.id)
                .sort(
                    (a: any, b: any) =>
                        new Date(a.date).getTime() - new Date(b.date).getTime(),
                )[0]
            if (fuelLog) {
                updateFuelLog({
                    variables: {
                        input: {
                            id: fuelLog.id,
                            fuelAfter: +fuelLog.fuelAfter,
                        },
                    },
                })
            }
        },
        onError: (error) => {
            console.error('Error updating fuel tank', error)
        },
    })

    const updateFuelLogs = (currentID: number = 0) => {
        if (fuelTankList) {
            Promise.all(
                fuelTankList?.map(async (fuelTank: any) => {
                    const variables = {
                        input: {
                            id: fuelTank.id,
                            currentLevel: fuelTank.currentLevel,
                        },
                    }
                    if (!currentEvent) {
                        if (offline) {
                            await fuelTankModel.save({
                                id: fuelTank.id,
                                currentLevel: fuelTank.currentLevel,
                            })
                        } else {
                            updateFuelTank({
                                variables: variables,
                            })
                        }
                    }
                    if (currentEvent) {
                        if (offline) {
                            await fuelLogModel.save({
                                id:
                                    currentEvent.eventType_Tasking.fuelLog.nodes.find(
                                        (log: any) => {
                                            log.fuelTank?.id === fuelTank.id
                                        },
                                    )?.id || generateUniqueId(),
                                fuelTankID: fuelTank.id,
                                fuelAfter: fuelTank.currentLevel,
                                date: dayjs().format('YYYY-MM-DD'),
                                eventType_TaskingID: currentID,
                            })
                        } else {
                            updateFuelLog({
                                variables: {
                                    input: {
                                        id: currentEvent.eventType_Tasking.fuelLog.nodes.find(
                                            (log: any) =>
                                                log.fuelTank.id === fuelTank.id,
                                        ).id,
                                        fuelTankID: fuelTank.id,
                                        fuelAfter: fuelTank.currentLevel,
                                        date: dayjs().format('YYYY-MM-DD'),
                                        eventType_TaskingID: currentID,
                                    },
                                },
                            })
                        }
                    } else {
                        if (offline) {
                            await fuelLogModel.save({
                                id: generateUniqueId(),
                                fuelTankID: fuelTank.id,
                                fuelAfter: fuelTank.currentLevel,
                                date: dayjs().format('YYYY-MM-DD'),
                                eventType_TaskingID: currentID,
                            })
                        } else {
                            createFuelLog({
                                variables: {
                                    input: {
                                        fuelTankID: fuelTank.id,
                                        fuelAfter: fuelTank.currentLevel,
                                        date: dayjs().format('YYYY-MM-DD'),
                                        eventType_TaskingID: currentID,
                                    },
                                },
                            })
                        }
                    }
                }),
            )
        }
    }

    const getInitialFuelLevel = (tank: any) => {
        if (fuelTankBuffer.length > 0) {
            const fuelTank = fuelTankBuffer.find(
                (item: any) => item.tankID === tank.id,
            )
            if (fuelTank) {
                return fuelTank.value
            }
        }
        if (tripReport.length > 0) {
            const fuelLogs = tripReport
                .map((trip: any) => {
                    return trip.tripEvents.nodes
                        .filter(
                            (event: any) =>
                                (event.eventCategory === 'Tasking' &&
                                    event.eventType_Tasking.fuelLog.nodes
                                        .length > 0) ||
                                (event.eventCategory ===
                                    'RefuellingBunkering' &&
                                    event.eventType_RefuellingBunkering.fuelLog
                                        .nodes.length > 0) ||
                                (event.eventCategory ===
                                    'PassengerDropFacility' &&
                                    event.eventType_PassengerDropFacility
                                        .fuelLog.nodes.length > 0),
                        )
                        .flatMap(
                            (event: any) =>
                                (event.eventCategory === 'Tasking' &&
                                    event.eventType_Tasking.fuelLog.nodes) ||
                                (event.eventCategory ===
                                    'RefuellingBunkering' &&
                                    event.eventType_RefuellingBunkering.fuelLog
                                        .nodes) ||
                                (event.eventCategory ===
                                    'PassengerDropFacility' &&
                                    event.eventType_PassengerDropFacility
                                        .fuelLog.nodes),
                        )
                })
                .flat()
            const lastFuelLog = fuelLogs
                ?.filter((log: any) => log.fuelTank.id === tank.id)
                .sort((a: any, b: any) => b.id - a.id)?.[0]
            if (lastFuelLog) {
                return lastFuelLog.fuelAfter
            }
        }
        // if (
        //     currentTrip &&
        //     currentTrip?.tripEvents?.nodes?.length > 0 &&
        //     currentTrip.tripEvents.nodes.find(
        //         (event: any) =>
        //             event.eventCategory === 'Tasking' &&
        //             event.eventType_Tasking.fuelLog.nodes.length > 0,
        //     )
        // ) {
        //     const fuelLog = currentTrip.tripEvents.nodes
        //         .filter(
        //             (event: any) =>
        //                 event.eventCategory === 'Tasking' &&
        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,
        //         )
        //         .sort((a: any, b: any) => b.id - a.id)[0]
        //         ?.eventType_Tasking.fuelLog.nodes.find(
        //             (log: any) => log.fuelTank.id === tank.id,
        //         )
        //     if (fuelLog) {
        //         return fuelLog.fuelAfter
        //     }
        // }
        // if (tripReport && tripReport.length > 1) {
        //     const latestTripFuelLog = tripReport
        //         .filter((trip: any) => trip.id < currentTrip.id)
        //         .sort((a: any, b: any) => b.id - a.id)[0]
        //         ?.tripEvents?.nodes.filter(
        //             (event: any) =>
        //                 event.eventCategory === 'Tasking' &&
        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,
        //         )
        //         .sort((a: any, b: any) => b.id - a.id)[0]
        //         ?.eventType_Tasking.fuelLog.nodes.find(
        //             (log: any) => log.fuelTank.id === tank.id,
        //         )
        //     if (latestTripFuelLog) {
        //         return latestTripFuelLog.fuelAfter
        //     }
        // }
        const fuelLog = updatedFuelLogs
            .filter((log: any) => log.fuelTank.id === tank.id)
            .sort(
                (a: any, b: any) =>
                    new Date(a.date).getTime() - new Date(b.date).getTime(),
            )[0]
        return fuelLog
            ? +tank.capacity > +fuelLog.fuelAfter
                ? +fuelLog.fuelAfter
                : +tank.capacity
            : +tank.currentLevel
    }

    const getFuelLogs = async (fuelLogIds: any) => {
        if (offline) {
            const data = await fuelTankModel.getByIds(fuelLogIds)
        } else {
            await queryGetFuelLogs({
                variables: {
                    id: fuelLogIds,
                },
            })
        }
    }

    const [queryGetFuelLogs] = useLazyQuery(GET_FUELLOGS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelLogs.nodes
            setUpdatedFuelLogs(data)
        },
        onError: (error: any) => {
            console.error('getFuelLogs error', error)
        },
    })

    useEffect(() => {
        getFuelLogs(fuelLogs.map((item: any) => item.id))
    }, [])

    return (
        <div className="w-full space-y-6">
            <>
                <div className="my-4 text-sm font-semibold uppercase">
                    {type === 'TaskingStartUnderway' &&
                        'Tasking start / underway'}
                    {type === 'TaskingPaused' && 'Tasking paused'}
                    {type === 'TaskingResumed' && 'Tasking resumed'}
                    {type === 'TaskingOnScene' && tasking.title}
                    {type === 'TaskingOnTow' && tasking.title}
                    {type === 'TaskingComplete' && tasking.title}
                </div>
                <P className="max-w-[40rem] mb-2">
                    Give this tasking a title and choose an operation type.
                    <br />
                    Recording fuel levels goes toward{' '}
                    <strong>
                        fuel reports for allocating to different operations
                    </strong>
                    .
                </P>
                {type === 'TaskingOnTow' && (
                    <P className="max-w-[40rem]">
                        Utilise attached checklist to ensure towing procedure is
                        followed and any risks identified.
                    </P>
                )}
                {type === 'TaskingOnTow' && (
                    <CheckFieldLabel
                        type="checkbox"
                        checked={allChecked}
                        className="w-fit"
                        variant="success"
                        leftContent={<AlertTriangle className="h-4 w-4" />}
                        rightContent={
                            <SquareArrowOutUpRight className="h-4 w-4" />
                        }
                        onClick={() => {
                            setOpenRiskAnalysis(true)
                        }}
                        label="Towing checklist - risk analysis"
                    />
                )}
                <div className="flex flex-col gap-2">
                    <div
                        className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                        <Label>Time when tasking takes place</Label>
                        <TimeField
                            time={time}
                            handleTimeChange={handleTimeChange}
                            timeID="time"
                            fieldName="Time"
                        />
                    </div>
                    {type === 'TaskingStartUnderway' && (
                        <div
                            className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                            <Label>Title of tasking</Label>
                            <Input
                                id="title"
                                name="title"
                                type="text"
                                value={tasking?.title ? tasking.title : ''}
                                placeholder="Title"
                                onChange={(e) => {
                                    setTasking({
                                        ...tasking,
                                        title: e.target.value,
                                    })
                                }}
                            />
                        </div>
                    )}
                    {fuelTankList &&
                        fuelTankList.map((tank: any) => (
                            <div
                                className={`${locked ? 'pointer-events-none' : ''} flex flex-col gap-2 my-4`}
                                key={tank.id}>
                                <div className="inline-flex">
                                    <SealogsFuelIcon className="size-6" />
                                    <Label>{tank.title}</Label>
                                </div>
                                <Input
                                    type="number"
                                    placeholder="Fuel end"
                                    value={
                                        tank.currentLevel ??
                                        (tasking?.fuelLog
                                            ? tasking.fuelLog.find(
                                                  (log: any) =>
                                                      +log.fuelTank.id ===
                                                      +tank.id,
                                              )?.fuelAfter
                                            : getInitialFuelLevel(tank)) ??
                                        0
                                    }
                                    min={0}
                                    max={tank.capacity}
                                    onChange={(e: any) =>
                                        handleUpdateFuelTank(
                                            tank,
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                        ))}
                    <div className={`${locked ? 'pointer-events-none' : ''}`}>
                        <div className="flex flex-col gap-2 my-4">
                            <Label>Location where tasking takes place</Label>
                            <LocationField
                                offline={offline}
                                setCurrentLocation={setCurrentLocation}
                                handleLocationChange={handleLocationChange}
                                currentEvent={tripEvent.eventType_Tasking}
                            />
                        </div>
                        {type !== 'TaskingPaused' &&
                        type !== 'TaskingResumed' &&
                        displayVessesRescueFields() ? (
                            <div className="my-4">
                                <Textarea
                                    id={`location-description`}
                                    rows={4}
                                    placeholder="Location description"
                                    value={locationDescription ?? ''}
                                    onChange={(e) => {
                                        setLocationDescription(e.target.value)
                                    }}
                                />
                            </div>
                        ) : (
                            <></>
                        )}
                    </div>
                    <div className={`${locked ? 'pointer-events-none' : ''}`}>
                        {type === 'TaskingPaused' && (
                            <>
                                <div className="my-4">
                                    <Combobox
                                        options={
                                            previousDropEvent
                                                ? previousDropEvent
                                                      .filter(
                                                          (group: any) =>
                                                              group
                                                                  .eventType_Tasking
                                                                  .status ===
                                                              'Open',
                                                      )
                                                      .map((group: any) => ({
                                                          value: group
                                                              .eventType_Tasking
                                                              .id,
                                                          label: `${group.eventType_Tasking.time} - ${group.eventType_Tasking.title}`,
                                                      }))
                                                : []
                                        }
                                        value={
                                            tasking.pausedTaskID > 0
                                                ? {
                                                      value: tasking.pausedTaskID,
                                                      label: `${
                                                          currentTrip?.tripEvents?.nodes.find(
                                                              (event: any) =>
                                                                  event?.eventType_TaskingID ==
                                                                  tasking.pausedTaskID,
                                                          )?.eventType_Tasking
                                                              ?.time
                                                      } - ${
                                                          currentTrip?.tripEvents?.nodes.find(
                                                              (event: any) =>
                                                                  event?.eventType_TaskingID ==
                                                                  tasking.pausedTaskID,
                                                          )?.eventType_Tasking
                                                              ?.title
                                                      }`,
                                                  }
                                                : taskingPausedValue
                                        }
                                        onChange={handleTaskingPauseChange}
                                        placeholder="Select Task to pause"
                                    />
                                </div>
                                <div className="my-4">
                                    {((selectedEvent && content) ||
                                        !selectedEvent) && (
                                        <Editor
                                            id="comment"
                                            placeholder="Comment"
                                            className="w-full"
                                            content={content}
                                            handleEditorChange={
                                                handleEditorChange
                                            }
                                        />
                                    )}
                                </div>
                            </>
                        )}
                        {type === 'TaskingComplete' && (
                            <div className="my-4">
                                <Combobox
                                    options={
                                        previousDropEvent
                                            ? previousDropEvent
                                                  .filter(
                                                      (group: any) =>
                                                          group
                                                              .eventType_Tasking
                                                              .status !==
                                                          'Completed',
                                                  )
                                                  .map((group: any) => ({
                                                      value: group
                                                          .eventType_Tasking.id,
                                                      label: `${group.eventType_Tasking.time} - ${group.eventType_Tasking.title}`,
                                                  }))
                                            : []
                                    }
                                    value={
                                        tasking.completedTaskID > 0
                                            ? {
                                                  value: tasking.completedTaskID,
                                                  label: `${
                                                      currentTrip?.tripEvents?.nodes.find(
                                                          (event: any) =>
                                                              event?.eventType_TaskingID ==
                                                              tasking.completedTaskID,
                                                      )?.eventType_Tasking.time
                                                  } - ${
                                                      currentTrip?.tripEvents?.nodes.find(
                                                          (event: any) =>
                                                              event?.eventType_TaskingID ==
                                                              tasking.completedTaskID,
                                                      )?.eventType_Tasking.title
                                                  }`,
                                              }
                                            : getPreviousTask(
                                                  taskingCompleteValue,
                                              )
                                    }
                                    onChange={handleTaskingCompleteChange}
                                    placeholder="Select Task to Close"
                                />
                            </div>
                        )}
                        {type === 'TaskingResumed' && (
                            <Combobox
                                options={
                                    previousDropEvent
                                        ? previousDropEvent
                                              .filter(
                                                  (group: any) =>
                                                      group.eventType_Tasking
                                                          .status === 'Paused',
                                              )
                                              .map((group: any) => ({
                                                  value: group.eventType_Tasking
                                                      .id,
                                                  label: `${group.eventType_Tasking.time} - ${group.eventType_Tasking.title}`,
                                              }))
                                        : []
                                }
                                value={
                                    tasking.openTaskID > 0
                                        ? {
                                              value: tasking.openTaskID,
                                              label: `${
                                                  currentTrip?.tripEvents?.nodes.find(
                                                      (event: any) =>
                                                          event?.eventType_TaskingID ==
                                                          tasking.openTaskID,
                                                  )?.eventType_Tasking.time
                                              } - ${
                                                  currentTrip?.tripEvents?.nodes.find(
                                                      (event: any) =>
                                                          event?.eventType_TaskingID ==
                                                          tasking.openTaskID,
                                                  )?.eventType_Tasking.title
                                              }`,
                                          }
                                        : taskingResumedValue
                                }
                                onChange={handleTaskingGroupChange}
                                placeholder="Select Task to continue"
                            />
                        )}
                        {type !== 'TaskingPaused' &&
                            type !== 'TaskingResumed' &&
                            type !== 'TaskingComplete' &&
                            type !== 'TaskingOnTow' &&
                            type !== 'TaskingOnScene' && (
                                <>
                                    {operationTypes && (
                                        <Combobox
                                            options={operationTypes.map(
                                                (type: any) => ({
                                                    value: type.value,
                                                    label: type.label,
                                                }),
                                            )}
                                            value={{
                                                value: currentOperationTypeValue(
                                                    tasking?.operationType,
                                                ),
                                                label: currentOperationTypeLabel(
                                                    tasking?.operationType,
                                                ),
                                            }}
                                            onChange={handleOperationTypeChange}
                                            placeholder="Operation type"
                                        />
                                    )}
                                </>
                            )}
                    </div>
                    <P className="max-w-[40rem]">
                        Everything else below this section is{' '}
                        <strong>optional can be completed later</strong>.
                        However, all the details loaded here will be used for
                        any tasking reports required.
                    </P>
                </div>
            </>
            {type !== 'TaskingPaused' &&
            type !== 'TaskingResumed' &&
            displayVessesRescueFields() ? (
                <VesselRescueFields
                    offline={offline}
                    geoLocations={geoLocations}
                    selectedEvent={findPreviousEvent(selectedEvent)}
                    locationDescription={locationDescription}
                    setLocationDescription={setLocationDescription}
                    closeModal={closeModal}
                    handleSaveParent={handleSave}
                    currentRescueID={findPreviousRescueID(
                        tasking.vesselRescueID,
                    )}
                    type={type}
                    eventCurrentLocation={{
                        currentLocation: currentLocation,
                        geoLocationID: tasking.geoLocationID,
                    }}
                    locked={locked}
                />
            ) : (
                <></>
            )}
            {type !== 'TaskingPaused' &&
            type !== 'TaskingResumed' &&
            displayPersonRescueFields() ? (
                <PersonRescueField
                    offline={offline}
                    geoLocations={geoLocations}
                    selectedEvent={findPreviousEvent(selectedEvent)}
                    closeModal={closeModal}
                    handleSaveParent={handleSave}
                    currentRescueID={findPreviousHumanRescueID(
                        tasking.personRescueID,
                    )}
                    type={type}
                    locked={locked}
                />
            ) : (
                <></>
            )}
            {type !== 'TaskingPaused' && type !== 'TaskingResumed' && (
                <>
                    <div className="my-4 text-sm font-semibold uppercase">
                        Incident type / number
                    </div>
                    <P className="max-w-[40rem]">
                        Detail if incident was tasked by Police, RCCNZ or
                        Coastguard and associated incident number if applicable
                    </P>
                    <div className="flex w-full items-start  grid-cols-1 md:grid-cols-5 md:grid-rows-2">
                        <div
                            className={`${locked ? 'pointer-events-none' : ''} mt-4 md:my-4 w-full flex items-center space-x-2 py-3`}>
                            <Checkbox
                                id="task-cgop"
                                checked={getIsCGOP(tasking?.cgop)}
                                onCheckedChange={(checked) => {
                                    if (checked)
                                        handleCgopChange({
                                            target: { value: 'on' },
                                        })
                                }}
                            />
                            <Label
                                htmlFor="task-cgop"
                                //className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 uppercase"
                                className="!mb-0">
                                CoastGuard Rescue
                            </Label>
                        </div>
                        <div
                            className={`${locked ? 'pointer-events-none' : ''} md:my-4 w-full md:col-span-4`}>
                            <Input
                                id="cgop"
                                type="text"
                                onChange={(e) => {
                                    setTasking({
                                        ...tasking,
                                        sarop: '',
                                        cgop: e.target.value,
                                    }),
                                        setCurrentIncident('cgop')
                                }}
                                value={getPreviousCGOP(tasking?.cgop) ?? ''}
                                aria-describedby="cgop-error"
                                required
                                placeholder="Police / RCCNZ number"
                            />
                        </div>
                        <div
                            className={`${locked ? 'pointer-events-none' : ''} mt-4 md:my-4 w-full flex items-center space-x-2 py-3`}>
                            <Checkbox
                                id="task-sarop"
                                checked={getIsSAROP(tasking?.sarop)}
                                onCheckedChange={(checked) => {
                                    if (checked)
                                        handleSaropChange({
                                            target: { value: 'on' },
                                        })
                                }}
                            />
                            <Label
                                htmlFor="task-sarop"
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 uppercase !mb-0">
                                SAROP
                            </Label>
                        </div>
                        <div
                            className={`${locked ? 'pointer-events-none' : ''} md:my-4 w-full md:col-span-4`}>
                            <Input
                                id="sarop"
                                type="text"
                                onChange={(e) => {
                                    setTasking({
                                        ...tasking,
                                        sarop: e.target.value,
                                        cgop: '',
                                    }),
                                        setCurrentIncident('sarop')
                                }}
                                value={getPreviousSAROP(tasking?.sarop) ?? ''}
                                aria-describedby="sarop-error"
                                required
                                placeholder="Police / RCCNZ number"
                            />
                        </div>
                    </div>
                </>
            )}
            {/* {(type === 'TaskingPaused' || type === 'TaskingResumed') && ( */}
            <div className="flex justify-end gap-2">
                <Button
                    variant="text"
                    iconLeft={X}
                    onClick={() => closeModal()}>
                    Cancel
                </Button>
                {!locked && (
                    <Button iconLeft={Check} onClick={() => handleSave(0, 0)}>
                        {selectedEvent ? 'Update' : 'Save'}
                    </Button>
                )}
            </div>
            {/* )} */}
            <AlertDialogNew
                openDialog={openNewLocationDialog}
                setOpenDialog={setOpenNewLocationDialog}
                actionText="Add New Location"
                handleCreate={handleCreateNewLocation}
                title="Add New Location">
                <div className="my-4 flex items-center">
                    <Input
                        id="new-location-title"
                        type="text"
                        aria-describedby="title-error"
                        required
                        placeholder="Location Title"
                    />
                </div>
                <div className="mb-4 flex items-center">
                    <Combobox
                        id="parent-location"
                        options={locations || []}
                        onChange={handleParentLocationChange}
                        placeholder="Parent Location (Optional)"
                        buttonClassName="w-full"
                    />
                </div>
                <div className="mb-4 flex items-center">
                    <Input
                        id="new-location-latitude"
                        type="text"
                        defaultValue={location.latitude}
                        aria-describedby="latitude-error"
                        required
                        placeholder="Latitude"
                    />
                </div>
                <div className="flex items-center">
                    <Input
                        id="new-location-longitude"
                        type="text"
                        defaultValue={location.longitude}
                        aria-describedby="longitude-error"
                        required
                        placeholder="Longitude"
                    />
                </div>
            </AlertDialogNew>

            <RiskAnalysis
                offline={offline}
                selectedEvent={findPreviousEvent(selectedEvent)}
                crewMembers={members}
                towingChecklistID={towingChecklistID}
                setTowingChecklistID={setTowingChecklistID}
                setAllChecked={setAllChecked}
                onSidebarClose={() => setOpenRiskAnalysis(false)}
                logBookConfig={undefined}
                currentTrip={undefined}
                open={openRiskAnalysis}
                onOpenChange={setOpenRiskAnalysis}
            />
        </div>
    )
}
