{"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "unused-imports"], "extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended"], "rules": {"unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}]}}