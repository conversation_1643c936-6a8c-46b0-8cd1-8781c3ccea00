'use client'

import { ReactNode } from 'react'
import { SidebarInset, SidebarProvider, SidebarTrigger } from './ui/sidebar'
import { AppSidebar } from './app-sidebar'
import { ScrollArea, Separator } from './ui'
import { BreadcrumbNavigation } from './ui/breadcrumb-navigation'
import RightSidebar from './right-sidebar'

interface IProps {
    children: ReactNode
}

export default function MainLayout({ children }: IProps) {
    return (
        <div className="flex flex-row bg-card phablet:bg-background min-h-screen max-h-screen h-full p-0 md:px-2 md:pt-2 lg:px-3 lg:pt-3 overflow-hidden w-auto text-foreground">
            <SidebarProvider>
                <AppSidebar />
                <SidebarInset>
                    <header className="flex h-12 shrink-0 items-center gap-2 px-4 border-b border-border text-sm">
                        <SidebarTrigger className="-ml-1 text-primary" />
                        <Separator
                            orientation="vertical"
                            className="mr-2 h-4"
                        />
                        <BreadcrumbNavigation />
                    </header>
                    <ScrollArea className="bg-card phablet:bg-background">
                        <div className="flex-1 flex flex-col p-3 small:p-4 phablet:p-5 lg:p-6 xl:p-8">
                            {children}
                        </div>
                    </ScrollArea>
                    <RightSidebar />
                </SidebarInset>
            </SidebarProvider>
        </div>
    )
}
