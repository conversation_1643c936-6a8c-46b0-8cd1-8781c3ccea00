"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./utils/responsiveLabel.ts":
/*!**********************************!*\
  !*** ./utils/responsiveLabel.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getResponsiveLabel: function() { return /* binding */ getResponsiveLabel; },\n/* harmony export */   useResponsiveLabel: function() { return /* binding */ useResponsiveLabel; }\n/* harmony export */ });\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n\n// Hook version - use this at the component level\nfunction useResponsiveLabel() {\n    let breakpoint = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"small\";\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_0__.useBreakpoints)();\n    return (short, long)=>bp[breakpoint] ? long : short;\n}\n// Utility version - use this when you have breakpoint state available\nfunction getResponsiveLabel(isAtBreakpoint, short, long) {\n    return isAtBreakpoint ? long : short;\n}\n// Legacy hook version for backward compatibility (but should be avoided in conditional rendering)\nfunction responsiveLabel(short, long) {\n    let breakpoint = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"tablet-md\";\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_0__.useBreakpoints)();\n    return bp[breakpoint] ? long : short;\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (responsiveLabel);\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./utils/responsiveLabel.ts\n"));

/***/ })

});