'use client'

import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import {
    CreateTripEvent,
    UpdateTripEvent,
    CREATE_OTHER_SHIP,
    CREATE_PILOT_TRANSFER,
    UPDATE_PILOT_TRANSFER,
    UPDATE_OTHER_SHIP,
} from '@/app/lib/graphQL/mutation'
import {
    CREW_LIST,
    GET_OTHER_SHIPS,
    GetTripEvent,
} from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'

import { useSearchParams } from 'next/navigation'
// import { classes } from '@/app/components/GlobalClasses'
import TripEventModel from '@/app/offline/models/tripEvent'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import TimeField from '../components/time'
import Select from 'react-select'
import { InputSkeleton } from '../../../../components/skeletons'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import RadioGroupField from '@/components/radio-group-field'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import { AlertDialogNew, H1 } from '@/components/ui'

export default function PilotTransfer({
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    locked,
    offline = false,
}: {
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    locked: any
    offline?: boolean
}) {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const [time, setTime] = useState<any>()
    const [content, setContent] = useState<any>('')
    const [pilotTransfer, setPilotTransfer] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [selectedShip, setSelectedShip] = useState<any>(null)
    const [otherShips, setOtherShips] = useState<any>([])
    const [newShip, setNewShip] = useState<any>({})
    const [pilots, setPilots] = useState<any>([])
    const [transferees, setTransferees] = useState<any>([])
    const [selectedPilots, setSelectedPilots] = useState<any>([])
    const [selectedTransferees, setSelectedTransferees] = useState<any>(null)
    const [transferType, setTransferType] = useState<any>(null)
    const [safetyBriefing, setSafetyBriefing] = useState<any>(null)
    const [openCreateShipDialog, setOpenCreateShipDialog] =
        useState<boolean>(false)
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })

    const tripEventModel = new TripEventModel()

    const [getOtherShip] = useLazyQuery(GET_OTHER_SHIPS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readOtherShips.nodes
            if (data) {
                const shipList = [
                    {
                        label: ' ---- Create Ship ---- ',
                        value: 'newShip',
                    },
                    ...data
                        ?.filter((category: any) => category.title !== null)
                        .map((category: any) => ({
                            label: category.title,
                            value: category.id,
                            details: category.details,
                        })),
                ]
                setOtherShips(shipList)
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const [getPilots] = useLazyQuery(CREW_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                const crewList = data
                    ?.filter((crew: any) => crew.firstName !== null)
                    .map((crew: any) => ({
                        label: `${crew.firstName} ${crew.surname}`,
                        value: crew.id,
                    }))
                setPilots(crewList)
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const [getTransferees] = useLazyQuery(CREW_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                const crewList = data
                    ?.filter((crew: any) => crew.firstName !== null)
                    .map((crew: any) => ({
                        label: `${crew.firstName} ${crew.surname}`,
                        value: crew.id,
                    }))
                setTransferees(crewList)
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleTimeChange = (date: any) => {
        setTime(dayjs(date))
    }

    useEffect(() => {
        getOtherShip()
        getPilots({
            variables: {
                filter: {
                    isPilot: { eq: true },
                },
            },
        })
        getTransferees({
            variables: {
                filter: {
                    isTransferee: { eq: true },
                },
            },
        })
    }, [])

    useEffect(() => {
        setPilotTransfer(false)
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
    }, [selectedEvent])

    useEffect(() => {
        setPilotTransfer(false)
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    const getCurrentEvent = async (id: any) => {
        if (offline) {
            // getTripEvent
            const event = await tripEventModel.getById(id)
            if (event) {
                setTripEvent(event)
                setPilotTransfer(event.pilotTransfer)
                setContent(event.pilotTransfer?.comment)
                setTime(dayjs(event.pilotTransfer?.transferTime))
                setSelectedShip({
                    label: event.pilotTransfer?.otherShip?.title,
                    value: event.pilotTransfer?.otherShip?.id,
                    details: event.pilotTransfer?.otherShip?.details,
                })
                setSelectedPilots(
                    event.pilotTransfer?.pilots?.nodes?.map((pilot: any) => ({
                        label: `${pilot.firstName} ${pilot.surname}`,
                        value: pilot.id,
                    })),
                )
                setSelectedTransferees(
                    event.pilotTransfer?.transferees?.nodes?.map(
                        (transferee: any) => ({
                            label: `${transferee.firstName} ${transferee.surname}`,
                            value: transferee.id,
                        }),
                    ),
                )
                setTransferType(event.pilotTransfer?.transferType == 'On')
                setSafetyBriefing(event.pilotTransfer?.safetyBriefing == true)
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTripEvent(event)
                setPilotTransfer(event.pilotTransfer)
                setContent(event.pilotTransfer?.comment)
                setTime(dayjs(event.pilotTransfer?.transferTime))
                setSelectedShip({
                    label: event.pilotTransfer?.otherShip?.title,
                    value: event.pilotTransfer?.otherShip?.id,
                    details: event.pilotTransfer?.otherShip?.details,
                })
                setSelectedPilots(
                    event.pilotTransfer?.pilots?.nodes?.map((pilot: any) => ({
                        label: `${pilot.firstName} ${pilot.surname}`,
                        value: pilot.id,
                    })),
                )
                setSelectedTransferees(
                    event.pilotTransfer?.transferees?.nodes?.map(
                        (transferee: any) => ({
                            label: `${transferee.firstName} ${transferee.surname}`,
                            value: transferee.id,
                        }),
                    ),
                )
                setTransferType(event.pilotTransfer?.transferType == 'On')
                setSafetyBriefing(event.pilotTransfer?.safetyBriefing == true)
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleSave = async () => {
        const variables = {
            input: {
                transferType: transferType ? 'On' : 'Off',
                transferTime: dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
                safetyBriefing: safetyBriefing ? true : false,
                comment: pilotTransfer?.comment,
                vesselID: +vesselID,
                logBookEntrySectionID: +currentTrip.id,
                otherShipID: +selectedShip?.value,
                pilots: selectedPilots
                    ?.map((pilot: any) => pilot.value)
                    .join(','),
                transferees: selectedTransferees
                    ?.map((transferee: any) => transferee.value)
                    .join(','),
            },
        }

        const otherShipVariables = {
            input: {
                id: +selectedShip?.value,
                title: selectedShip?.label,
                registration: selectedShip?.registration,
                details: selectedShip?.details,
            },
        }
        updateOtherShip({
            variables: otherShipVariables,
        })
        if (currentEvent) {
            if (offline) {
                // updateTripEvent
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'PilotTransfer',
                    logBookEntrySectionID: currentTrip.id,
                })

                getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'PilotTransfer',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
            if (offline) {
                // updatePilotTransfer
                // const data = await pilotTransferModel.save({
                //     id: +selectedEvent?.pilotTransfer?.id,
                //     ...variables.input,
                // })
            } else {
                updatePilotTransfer({
                    variables: {
                        input: {
                            id: +pilotTransfer?.id,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                // createTripEvent
                const data = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'PilotTransfer',
                    logBookEntrySectionID: currentTrip.id,
                })

                setCurrentEvent(data)
                // createPilotTransfer
                // const pilotTransferData = await pilotTransferModel.save({
                //     id: generateUniqueId(),
                //     geoLocationID: pilotTransfer?.geoLocationID,
                //     notes: content,
                //     lat: currentLocation.latitude.toString(),
                //     long: currentLocation.longitude.toString(),
                //     date: dayjs(time).format('YYYY-MM-DDTHH:mm:ssZ'),
                // })
                setTimeout(async () => {
                    // updateTripEvent
                    // const x = await tripEventModel.save({
                    //     id: currentEvent?.id,
                    //     pilotTransferID: pilotTransferData.id,
                    // })

                    getCurrentEvent(currentEvent?.id)
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            currentTrip.id,
                        ],
                    })
                }, 200)
                closeModal()
                // updateTripEvent
                await tripEventModel.save({
                    id: data.id,
                    eventCategory: 'PilotTransfer',
                    pilotTransferID: currentTrip.id,
                })

                getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'PilotTransfer',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setCurrentEvent(data)
            createPilotTransfer({
                variables: {
                    input: {
                        transferType: transferType ? 'On' : 'Off',
                        transferTime: dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
                        safetyBriefing: safetyBriefing ? true : false,
                        comment: pilotTransfer?.comment,
                        vesselID: +vesselID,
                        logBookEntrySectionID: +currentTrip.id,
                        otherShipID: +selectedShip?.value,
                        tripEventID: +data.id,
                        pilots: selectedPilots
                            ?.map((pilot: any) => pilot.value)
                            .join(','),
                        transferees: selectedTransferees
                            ?.map((transferee: any) => transferee.value)
                            .join(','),
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createPilotTransfer] = useMutation(CREATE_PILOT_TRANSFER, {
        onCompleted: (response) => {
            const data = response.createPilotTransfer
            setTimeout(() => {
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEvent?.id,
                            pilotTransferID: data.id,
                        },
                    },
                })
            }, 200)
            closeModal()
        },
        onError: (error) => {
            console.error('Error creating refuelling', error)
        },
    })

    const [updatePilotTransfer] = useMutation(UPDATE_PILOT_TRANSFER, {
        onCompleted: (response) => {
            const data = response.updatePilotTransfer
        },
        onError: (error) => {
            console.error('Error updating refuelling', error)
        },
    })

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const handleSetSelectedShip = (value: any) => {
        if (value.value === 'newShip') {
            setOpenCreateShipDialog(true)
        } else {
            setSelectedShip(value)
        }
    }

    const handleCreateShip = async () => {
        if (newShip) {
            const variables = {
                input: {
                    title: newShip.title,
                    registration: newShip.registration,
                    details: newShip.details,
                },
            }
            await createOtherShip({
                variables: variables,
            })
        }
    }

    const [createOtherShip] = useMutation(CREATE_OTHER_SHIP, {
        onCompleted: (response) => {
            const data = response.createOtherShip
            const newShip = {
                label: data.title,
                value: data.id,
                details: data.details,
            }
            setOtherShips((prevState: any) => [...prevState, newShip])
            setSelectedShip(newShip)
            setNewShip({})
            setOpenCreateShipDialog(false)
        },
        onError: (error) => {
            console.error('Error creating ship', error)
        },
    })

    const [updateOtherShip] = useMutation(UPDATE_OTHER_SHIP, {
        onCompleted: (response) => {
            const data = response.updateOtherShip
            const newShip = {
                label: selectedShip.label,
                value: selectedShip.value,
                details: selectedShip.details,
            }
            setOtherShips((prevState: any) =>
                prevState.map((ship: any) =>
                    ship.value === newShip.value ? newShip : ship,
                ),
            )
            setSelectedShip(newShip)
            setNewShip({})
            setOpenCreateShipDialog(false)
        },
        onError: (error) => {
            console.error('Error creating ship', error)
        },
    })

    return (
        <div className="w-full">
            <div className="my-4 text-sm font-semibold uppercase">
                Pilot Transfer
            </div>
            <div className="flex flex-col gap-2">
                <div className={`${locked ? 'pointer-events-none' : ''}`}>
                    <Label className={`!w-full`}>Ship</Label>
                    {otherShips ? (
                        <Select
                            id="other-ship"
                            closeMenuOnSelect={false}
                            options={otherShips}
                            value={selectedShip}
                            menuPlacement="top"
                            onChange={handleSetSelectedShip}
                            className="w-full bg-slblue-50 rounded text-sm"
                            classNames={{
                                control: () =>
                                    'block py-1 w-full !text-sm !text-gray-900 !bg-transparent !rounded-lg !border !border-gray-200 focus:ring-blue-500 focus:border-blue-500   ',
                                singleValue: () => '',
                                dropdownIndicator: () => '!p-0 !hidden',
                                menu: () => '',
                                indicatorSeparator: () => '!hidden',
                                multiValue: () =>
                                    '!bg-slblue-100 inline-block rounded p-0.5 py-px m-0 !mr-1.5 border border-slblue-300 !rounded-md !text-slblue-900 font-normal mr-2',
                                clearIndicator: () => '!py-0',
                                valueContainer: () => '!py-0',
                                input: () => '!py-1',
                            }}
                        />
                    ) : (
                        <InputSkeleton />
                    )}
                </div>
                <div className={`${locked ? 'pointer-events-none' : ''}`}>
                    <Label className={` !w-full`}>Details</Label>
                    <Textarea
                        id={`trip-update-notes`}
                        placeholder="Details"
                        value={selectedShip?.details}
                        onChange={(e) =>
                            setSelectedShip({
                                ...selectedShip,
                                details: e.target.value,
                            })
                        }
                    />
                </div>
                {pilots && pilots.length > 0 && (
                    <div className={`${locked ? 'pointer-events-none' : ''}`}>
                        <Label className={`!w-full`}>Pilot</Label>
                        <Select
                            id="pilot"
                            closeMenuOnSelect={false}
                            options={pilots}
                            value={selectedPilots}
                            isMulti
                            menuPlacement="top"
                            onChange={(e) => setSelectedPilots(e)}
                            className="w-full bg-slblue-50 rounded text-sm"
                            classNames={{
                                control: () =>
                                    'block py-1 w-full !text-sm !text-gray-900 !bg-transparent !rounded-lg !border !border-gray-200 focus:ring-blue-500 focus:border-blue-500   ',
                                singleValue: () => '',
                                dropdownIndicator: () => '!p-0 !hidden',
                                menu: () => '',
                                indicatorSeparator: () => '!hidden',
                                multiValue: () =>
                                    '!bg-slblue-100 inline-block rounded p-0.5 py-px m-0 !mr-1.5 border border-slblue-300 !rounded-md !text-slblue-900 font-normal mr-2',
                                clearIndicator: () => '!py-0',
                                valueContainer: () => '!py-0',
                                input: () => '!py-1',
                            }}
                        />
                    </div>
                )}
                {/* {transferees && transferees.length > 0 && (
                    <div className={`${locked ? 'pointer-events-none' : ''}`}>
                        <Label className={`!w-full`}>Transferees</Label>
                        <Select
                            id="transferees"
                            closeMenuOnSelect={false}
                            options={transferees}
                            value={selectedTransferees}
                            isMulti
                            menuPlacement="top"
                            onChange={(e) => setSelectedTransferees(e)}
                            className="w-full bg-slblue-50 rounded text-sm"
                            classNames={{
                                control: () =>
                                    'block py-1 w-full !text-sm !text-gray-900 !bg-transparent !rounded-lg !border !border-gray-200 focus:ring-blue-500 focus:border-blue-500   ',
                                singleValue: () => '',
                                dropdownIndicator: () => '!p-0 !hidden',
                                menu: () => '',
                                indicatorSeparator: () => '!hidden',
                                multiValue: () =>
                                    '!bg-slblue-100 inline-block rounded p-0.5 py-px m-0 !mr-1.5 border border-slblue-300 !rounded-md !text-slblue-900 font-normal mr-2',
                                clearIndicator: () => '!py-0',
                                valueContainer: () => '!py-0',
                                input: () => '!py-1',
                            }}
                        />
                    </div>
                )} */}
                <div
                    className={`${locked ? 'pointer-events-none' : ''} flex flex-row items-center gap-2`}>
                    <Label>Transfer type</Label>
                    <div>
                        <RadioGroupField
                            locked={false}
                            checked={transferType}
                            inputId="transfer-type"
                            yesLabel="On"
                            noLabel="Off"
                            handleYesChange={() => setTransferType(true)}
                            handleNoChange={() => setTransferType(false)}
                        />
                    </div>
                </div>
                <div className={`${locked ? 'pointer-events-none' : ''}`}>
                    <Label className={`!w-full`}>Transfer time</Label>
                    <TimeField
                        time={dayjs(time).format('HH:mm')}
                        handleTimeChange={handleTimeChange}
                        timeID="transfer-time"
                        fieldName="Transfer time"
                    />
                </div>
                <div
                    className={`${locked ? 'pointer-events-none' : ''} flex flex-row items-center gap-2`}>
                    <Label>Safety breifing</Label>
                    <div>
                        <RadioGroupField
                            locked={false}
                            checked={safetyBriefing}
                            inputId="safetyBriefing"
                            yesLabel="Yes"
                            noLabel="No"
                            handleYesChange={() => setSafetyBriefing(true)}
                            handleNoChange={() => setSafetyBriefing(false)}
                        />
                    </div>
                </div>
                <div className={`${locked ? 'pointer-events-none' : ''}`}>
                    <Label className={`!w-full`}>Notes</Label>
                    <Textarea
                        id={`pilot-transfer-notes`}
                        placeholder="Notes"
                        value={pilotTransfer?.comment}
                        onChange={(e) =>
                            setPilotTransfer({
                                ...pilotTransfer,
                                comment: e.target.value,
                            })
                        }
                    />
                </div>
            </div>
            <hr className="my-2" />
            <div className=""></div>
            <div className="flex justify-end">
                <SeaLogsButton
                    text="Cancel"
                    type="text"
                    action={() => closeModal()}
                />
                <SeaLogsButton
                    text={selectedEvent ? 'Update' : 'Save'}
                    type="primary"
                    color="sky"
                    icon="check"
                    action={locked ? () => {} : handleSave}
                />
            </div>
            <AlertDialogNew
                openDialog={openCreateShipDialog}
                setOpenDialog={setOpenCreateShipDialog}
                handleCreate={handleCreateShip}
                actionText="Create">
                <H1
                    slot="title"
                    className="text-2xl font-light leading-6 my-2 text-sldarkblue-800">
                    Create New Ship
                </H1>
                <div className="my-4 flex items-center flex-col gap-3">
                    <Input
                        id={`new-ship`}
                        type="text"
                        placeholder="Title"
                        onChange={(e) =>
                            setNewShip({ ...newShip, title: e.target.value })
                        }
                    />
                    <Input
                        id={`new-ship-registration`}
                        type="text"
                        placeholder="Registration"
                        onChange={(e) =>
                            setNewShip({
                                ...newShip,
                                registration: e.target.value,
                            })
                        }
                    />
                    <Textarea
                        id={`new-ship-details`}
                        placeholder="Details"
                        onChange={(e) =>
                            setNewShip({
                                ...newShip,
                                details: e.target.value,
                            })
                        }
                    />
                </div>
            </AlertDialogNew>
        </div>
    )
}
