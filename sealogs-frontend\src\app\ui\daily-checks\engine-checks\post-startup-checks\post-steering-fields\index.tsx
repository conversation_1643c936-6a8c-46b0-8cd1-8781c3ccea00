'use client'

import { Dispatch, useMemo, useState } from 'react'
import { displayField, getFilteredFields } from '../../../actions'
import { useEngineFields } from '../../use-engine-fields'
import CrewChecker from '../../../crew-checker/crew-checker'
import PostSteering<PERSON><PERSON><PERSON>ield from './post-steering-group-field'
import dayjs from 'dayjs'

interface IProps {
    logentryID: any
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    offline?: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
    updateVesselDailyCheck_LogBookEntrySection: Function
    fieldImages: any
    refreshImages: any
}

export default function PostSteeringFields({
    logentryID,
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    offline = false,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
    updateVesselDailyCheck_LogBookEntrySection,
    fieldImages,
    refreshImages,
}: IProps) {
    const { postSteeringFields } = useEngineFields(
        logBookConfig,
        vesselDailyCheck,
    )

    const filteredFields = useMemo(() => {
        return (
            getFilteredFields(postSteeringFields, true, logBookConfig)?.filter(
                (groupField: any) =>
                    displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [])

    const [postCrewResponsible, setPostCrewResponsible] = useState<any>(
        vesselDailyCheck?.postCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )

    const handlePostCrewResponsible = async (crews: any) => {
        setPostCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                postCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log(' offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const [postCheckTime, setPostCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.postCheckTime ?? new Date()),
    )

    const handlePostCheckTime = async (date: any) => {
        setPostCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                postCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log(' offline')
                /* const newVesselDailyCheck =
                            await dailyCheckModel.save(variables)
                        // setSaving(true)
                        setSaving(false)
                        setVesselDailyCheck([newVesselDailyCheck])
                        const sections = logbook.logBookEntrySections.nodes
                        const section = {
                            className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                            id: `${vesselDailyCheck.id}`,
                            logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                            __typename: 'VesselDailyCheck_LogBookEntrySection',
                        }
                        if (
                            !sections.some(
                                (s: any) =>
                                    JSON.stringify(s) === JSON.stringify(section),
                            )
                        ) {
                            sections.push(section)
                        }
                        const lb = {
                            ...logbook,
                            logBookEntrySections: { nodes: sections },
                        }
                        await logBookModel.save(lb)
                        getOfflineLogBookEntry() */
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    if (filteredFields.length === 0) {
        return <></>
    }

    return (
        <>
            {logBookConfig && vesselDailyCheck && (
                <>
                    {filteredFields.map((groupField: any) => (
                        <PostSteeringGroupField
                            key={groupField.name}
                            edit_logBookEntry={edit_logBookEntry}
                            getComment={getComment}
                            groupField={groupField}
                            handleEngineChecks={handleEngineChecks}
                            locked={locked}
                            logBookConfig={logBookConfig}
                            setDescriptionPanelContent={
                                setDescriptionPanelContent
                            }
                            setDescriptionPanelHeading={
                                setDescriptionPanelHeading
                            }
                            setOpenDescriptionPanel={setOpenDescriptionPanel}
                            showCommentPopup={showCommentPopup}
                            fieldImages={fieldImages}
                            refreshImages={refreshImages}
                        />
                    ))}
                </>
            )}
            <div className="h-8" />
            <CrewChecker
                crewKey="PostCrewResponsible"
                timeKey="PostCheckTime"
                logBookConfig={logBookConfig}
                locked={locked}
                offline={offline}
                edit_logBookEntry={edit_logBookEntry}
                setCrewResponsible={handlePostCrewResponsible}
                crewResponsible={postCrewResponsible}
                checkTime={postCheckTime}
                handleCheckTime={handlePostCheckTime}
                setCheckTime={setPostCheckTime}
            />
        </>
    )
}
