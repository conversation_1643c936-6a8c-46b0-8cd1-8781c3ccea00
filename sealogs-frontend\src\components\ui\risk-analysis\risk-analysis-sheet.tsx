'use client'

import React, { useState, useEffect } from 'react'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { useToast } from '@/hooks/use-toast'
import { Plus, Check, InfoIcon, ArrowLeft } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { P } from '@/components/ui/typography'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
    SheetBody,
    SheetFooter,
} from '@/components/ui/sheet'
import { AlertDialogNew } from '@/components/ui'

export interface RiskAnalysisContentProps {
    /**
     * The checklist fields to display
     */
    checkFields: Array<{
        name: string
        label: string
        value: string
        checked: boolean
        handleChange: (checked: boolean) => void
        description?: React.ReactNode
    }>

    /**
     * The risk factors to display
     */
    riskFactors?: Array<any>

    /**
     * The crew members to display in the author dropdown
     */
    crewMembers?: Array<any>

    /**
     * The selected author
     */
    selectedAuthor?: any

    /**
     * Callback when the author is changed
     */
    onAuthorChange?: (author: any) => void

    /**
     * Whether the user has permission to edit the risk analysis
     */
    canEdit?: boolean

    /**
     * Whether the user has permission to delete risks
     */
    canDeleteRisks?: boolean

    /**
     * Callback when a risk is clicked
     */
    onRiskClick?: (risk: any) => void

    /**
     * Callback when the add risk button is clicked
     */
    onAddRiskClick?: () => void

    /**
     * Callback when a risk is deleted
     */
    onRiskDelete?: (risk: any) => void

    /**
     * Function to set whether all fields are checked
     */
    setAllChecked?: (allChecked: boolean) => void
}

export interface RiskAnalysisSheetProps extends RiskAnalysisContentProps {
    /**
     * Whether the sheet is open
     */
    open: boolean

    /**
     * Callback when the open state changes
     */
    onOpenChange: (open: boolean) => void

    /**
     * Callback when the sidebar is closed
     */
    onSidebarClose: () => void

    /**
     * The title of the risk analysis
     */
    title: string

    /**
     * The subtitle of the risk analysis
     */
    subtitle?: string
}

export function RiskAnalysisContent({
    checkFields,
    riskFactors = [],
    crewMembers = [],
    selectedAuthor,
    onAuthorChange,
    canEdit = true,
    canDeleteRisks = true,
    onRiskClick,
    onAddRiskClick,
    onRiskDelete,
    setAllChecked,
}: RiskAnalysisContentProps) {
    const { toast } = useToast()
    const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false)
    const [riskToDelete, setRiskToDelete] = useState<any>(null)

    // Check if all fields are checked
    useEffect(() => {
        if (setAllChecked) {
            setAllChecked(checkFields.every((field) => field.checked))
        }
    }, [checkFields, setAllChecked])

    const handleRiskClick = (risk: any) => {
        if (!canEdit) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'You do not have permission to edit this section',
            })
            return
        }

        if (onRiskClick) {
            onRiskClick(risk)
        }
    }

    const handleAddRiskClick = () => {
        if (!canEdit) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'You do not have permission to edit this section',
            })
            return
        }

        if (onAddRiskClick) {
            onAddRiskClick()
        }
    }

    const handleDeleteRisk = (risk: any) => {
        if (!canEdit || !canDeleteRisks) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'You do not have permission to delete risks',
            })
            return
        }

        setRiskToDelete(risk)
        setOpenDeleteConfirmation(true)
    }

    const confirmDeleteRisk = () => {
        if (onRiskDelete && riskToDelete) {
            onRiskDelete(riskToDelete)
        }
        setOpenDeleteConfirmation(false)
    }

    return (
        <>
            <div className="grid space-y-0 md:grid-cols-2 gap-2.5">
                <div className="h-full flex flex-col min-h-[400px] overflow-auto">
                    <ScrollArea className="h-full mb-5 border border-dashed border-border rounded-lg">
                        <div className="h-full p-2 sm:p-5 space-y-2">
                            {checkFields.map((field, index) => (
                                <div
                                    key={`${index}-${field.name}`}
                                    className="space-y-1">
                                    <CheckFieldLabel
                                        id={`${field.value}-onChangeComplete-${index}`}
                                        type="checkbox"
                                        checked={field.checked}
                                        onCheckedChange={(checked: boolean) => {
                                            field.handleChange(checked === true)
                                        }}
                                        variant="warning"
                                        label={field.label}
                                        rightContent={
                                            field.description && (
                                                <Popover>
                                                    <PopoverTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            onClick={(e) =>
                                                                e.stopPropagation()
                                                            }
                                                            size="icon"
                                                            iconOnly
                                                            iconLeft={
                                                                <InfoIcon
                                                                    className="text-curious-blue-900 fill-curious-blue-50"
                                                                    size={24}
                                                                />
                                                            }>
                                                            <span className="sr-only">
                                                                View description
                                                            </span>
                                                        </Button>
                                                    </PopoverTrigger>
                                                    <PopoverContent className="w-72 p-3">
                                                        <ul>
                                                            <li>
                                                                {
                                                                    field.description
                                                                }
                                                            </li>
                                                        </ul>
                                                    </PopoverContent>
                                                </Popover>
                                            )
                                        }
                                    />
                                </div>
                            ))}
                        </div>
                    </ScrollArea>

                    {/* Author selection */}
                    {crewMembers.length > 0 && (
                        <Label label="Who completed risk" htmlFor="author">
                            <Combobox
                                id="author"
                                options={crewMembers.map((member) => ({
                                    ...member,
                                    profile: member.profile || {
                                        firstName: member.label?.split(' ')[0],
                                        surname: member.label
                                            ?.split(' ')
                                            .slice(1)
                                            .join(' '),
                                        avatar:
                                            member.avatar ||
                                            member.profileImage,
                                    },
                                }))}
                                value={selectedAuthor}
                                placeholder="Select crew"
                                onChange={(option) => {
                                    if (onAuthorChange) {
                                        onAuthorChange(option)
                                    }
                                }}
                            />
                        </Label>
                    )}
                </div>
                <div className="h-full col-span-1 min-h-[400px] flex flex-col overflow-auto">
                    <ScrollArea className="h-full border border-dashed border-border mb-4 rounded-lg">
                        <div className="h-full p-2 sm:p-5">
                            {riskFactors.length > 0 &&
                                riskFactors.map((risk: any) => (
                                    <div
                                        key={`${risk.id}-risk-analysis`}
                                        className="flex items-center justify-between mb-4 p-3 rounded-md border border-dashed border-border">
                                        <div
                                            className="cursor-pointer"
                                            onClick={() =>
                                                handleRiskClick(risk)
                                            }>
                                            <Label
                                                label={risk.title}
                                                className="font-medium">
                                                {risk?.impact && (
                                                    <div className="flex flex-col">
                                                        <P className="w-full leading-tight text-sm font-normal rounded-lg">
                                                            Impact:{' '}
                                                            {risk.impact}
                                                        </P>
                                                        <P className="w-full leading-tight text-sm font-normal rounded-lg">
                                                            Probability:{' '}
                                                            {risk.probability}
                                                            /10
                                                        </P>
                                                    </div>
                                                )}
                                            </Label>
                                        </div>

                                        <div className="flex items-center gap-2.5">
                                            {risk?.mitigationStrategy?.nodes
                                                ?.length > 0 && (
                                                <Popover>
                                                    <PopoverTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            iconOnly
                                                            iconLeft={
                                                                <InfoIcon
                                                                    className="text-light-blue-vivid-900 fill-light-blue-vivid-50"
                                                                    size={24}
                                                                />
                                                            }>
                                                            <span className="sr-only">
                                                                View strategies
                                                            </span>
                                                        </Button>
                                                    </PopoverTrigger>
                                                    <PopoverContent className="w-72 p-3">
                                                        {risk?.mitigationStrategy?.nodes.map(
                                                            (s: any) => (
                                                                <div
                                                                    key={s.id}
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: s.strategy,
                                                                    }}
                                                                />
                                                            ),
                                                        )}
                                                    </PopoverContent>
                                                </Popover>
                                            )}
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() =>
                                                    handleDeleteRisk(risk)
                                                }>
                                                <span className="sr-only">
                                                    Delete risk
                                                </span>
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                        </div>
                    </ScrollArea>
                    <Button
                        variant="outline"
                        iconLeft={Plus}
                        onClick={handleAddRiskClick}>
                        Add Risk
                    </Button>
                </div>
            </div>

            {/* Delete confirmation dialog */}
            <AlertDialogNew
                openDialog={openDeleteConfirmation}
                setOpenDialog={setOpenDeleteConfirmation}
                handleCreate={confirmDeleteRisk}
                actionText="Delete"
                title="Delete Risk"
                variant="warning"
                size="lg"
                position="center"
                showIcon={true}>
                <P>
                    Are you sure you want to delete this risk? This action
                    cannot be undone.
                </P>
            </AlertDialogNew>
        </>
    )
}

export function RiskAnalysisSheet({
    open,
    onOpenChange,
    onSidebarClose,
    title,
    subtitle,
    checkFields,
    riskFactors = [],
    crewMembers = [],
    selectedAuthor,
    onAuthorChange,
    canEdit = true,
    canDeleteRisks = true,
    onRiskClick,
    onAddRiskClick,
    onRiskDelete,
    setAllChecked,
}: RiskAnalysisSheetProps) {
    return (
        <>
            <Sheet open={open} onOpenChange={onOpenChange}>
                <SheetContent side="right" className="w-[90vw] sm:w-[60vw]">
                    <SheetHeader>
                        <SheetTitle>
                            {title}{' '}
                            {subtitle && (
                                <span className="font-thin">{subtitle}</span>
                            )}
                        </SheetTitle>
                    </SheetHeader>
                    <SheetBody>
                        <RiskAnalysisContent
                            checkFields={checkFields}
                            riskFactors={riskFactors}
                            crewMembers={crewMembers}
                            selectedAuthor={selectedAuthor}
                            onAuthorChange={onAuthorChange}
                            canEdit={canEdit}
                            canDeleteRisks={canDeleteRisks}
                            onRiskClick={onRiskClick}
                            onAddRiskClick={onAddRiskClick}
                            onRiskDelete={onRiskDelete}
                            setAllChecked={setAllChecked}
                        />
                    </SheetBody>
                    <SheetFooter>
                        <div className="flex gap-2 justify-end">
                            <Button
                                variant="back"
                                iconLeft={<ArrowLeft />}
                                onClick={() => onOpenChange(false)}>
                                Cancel
                            </Button>
                            <Button
                                variant="primary"
                                iconLeft={Check}
                                onClick={onSidebarClose}>
                                Save
                            </Button>
                        </div>
                    </SheetFooter>
                </SheetContent>
            </Sheet>
        </>
    )
}
