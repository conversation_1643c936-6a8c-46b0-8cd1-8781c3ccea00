'use client'

import React, { use, useEffect, useRef, useState } from 'react'
import AWS from 'aws-sdk'
import { AlertDialogNew, Button } from '@/components/ui'
import { toast } from '@/hooks/use-toast'
import { Camera } from 'lucide-react'
import { cn } from '../../../../../utils/cn'
import { useSearchParams } from 'next/navigation'
import { useMutation } from '@apollo/client'
import { CREATE_SECTION_MEMBER_IMAGE } from '@/app/lib/graphQL/mutation'
import UploadCloudFlare from './upload-cf'

const ACCOUNT_ID = 'ddde1c1cd1aa25641691808dcbafdeb7'
const ACCESS_KEY_ID = '06c3e13a539f24e6fdf7075bf381bf5e'
const SECRET_ACCESS_KEY =
    '0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8'

const s3Client = new AWS.S3({
    endpoint: `https://${ACCOUNT_ID}.r2.cloudflarestorage.com`,
    accessKeyId: ACCESS_KEY_ID,
    secretAccessKey: SECRET_ACCESS_KEY,
    signatureVersion: 'v4',
    region: 'auto',
})

export default function UploadCloudFlareCaptures({
    file = false,
    setFile,
    inputId,
}: {
    file?: string | boolean
    setFile: any
    inputId?: string
}) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const [openCameraDialog, setOpenCameraDialog] = useState(false)
    const [image, setImage] = useState<any>(false)
    const [displayImage, setDisplayImage] = useState(false)
    const [clientID, setClientID] = useState(0)
    const [devices, setDevices] = useState<MediaDeviceInfo[]>([])
    const [fileUpload, setFileUpload] = useState(false)
    const [uploadFileName, setUploadFileName] = useState(false)

    const getFile = (file: any) => {
        s3Client.getObject(
            {
                Bucket: 'captures',
                Key: file,
            },
            async (err, data) => {
                if (err) {
                    console.error(err)
                } else {
                    const fileType = file.split('.').pop() || ''
                    const blob = new Blob([data?.Body as Uint8Array])
                    const url = URL.createObjectURL(blob)
                    if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {
                        setImage(url)
                        setDisplayImage(true)
                    } else {
                        const textContent = new TextDecoder().decode(
                            data?.Body as Uint8Array,
                        )
                        setImage(textContent)
                        setDisplayImage(true)
                    }
                }
            },
        )
    }

    // Handle opening the camera dialog
    const handleOpenCameraDialog = async (bypass = false) => {
        setOpenCameraDialog(true)
        setUploadFileName(false)
        setFileUpload(false)
        setImage(null)
        setDisplayImage(false)
        if (file && !bypass) {
            getFile(file)
            return
        }
        const devices = await navigator.mediaDevices.enumerateDevices()
        const hasEnvironmentCamera = devices.some(
            (device) => device.kind === 'videoinput',
        )
        if (hasEnvironmentCamera) {
            setDevices(devices.filter((device) => device.kind === 'videoinput'))
        } else {
            toast({
                description: 'No camera found. Please connect a camera.',
                variant: 'destructive',
            })
            return
        }
        navigator.mediaDevices
            .getUserMedia({
                video: {
                    facingMode: 'environment',
                },
                audio: false,
            })
            .then((stream) => {
                const videoElement = document.getElementById(
                    'camera-video',
                ) as HTMLVideoElement
                videoElement.srcObject = stream
                videoElement.play()
            })
            .catch((error) => {
                console.error('Error accessing camera:', error)
            })
    }

    const captureImage = () => {
        const videoElement = document.getElementById(
            'camera-video',
        ) as HTMLVideoElement
        if (!videoElement) {
            console.error('Video element not found')
            return
        }
        const canvas = document.createElement('canvas')
        canvas.width = videoElement.videoWidth
        canvas.height = videoElement.videoHeight
        const context = canvas.getContext('2d')
        if (!context) {
            console.error('Failed to get canvas context')
            return
        }
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height)
        const imageData = canvas.toDataURL('image/png')

        // Stop the camera stream after capturing the image
        if (videoElement.srcObject) {
            const stream = videoElement.srcObject as MediaStream
            const tracks = stream.getTracks()
            tracks.forEach((track) => track.stop())
            videoElement.srcObject = null
        }
        if (imageData) {
            setImage(imageData)
            setDisplayImage(true)
        }
    }

    const turnOffCamera = () => {
        const videoElement = document.getElementById(
            'camera-video',
        ) as HTMLVideoElement
        if (videoElement && videoElement.srcObject) {
            const stream = videoElement.srcObject as MediaStream
            const tracks = stream.getTracks()
            tracks.forEach((track) => track.stop())
            videoElement.srcObject = null
        }
    }

    useEffect(() => {
        setClientID(+(localStorage.getItem('clientId') ?? 0))
    }, [])

    useEffect(() => {
        if (openCameraDialog) return
        turnOffCamera()
    }, [openCameraDialog])

    const [createSectionMemberImage] = useMutation(
        CREATE_SECTION_MEMBER_IMAGE,
        {
            onCompleted: (response) => {
                const data = response.createSectionMemberImage
                setFile()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    async function uploadFile(file: any) {
        // Upload file to Cloudflare
        var fileName = file?.name || Date.now()
        fileName = uploadFileName ? uploadFileName : clientID + '-capture-' + fileName
        createSectionMemberImage({
            variables: {
                input: {
                    name: fileName,
                    fieldName: inputId,
                    imageType: 'FieldImage',
                    logBookEntryID: logentryID,
                },
            },
        })
        !uploadFileName &&
            s3Client.putObject(
                {
                    Bucket: 'captures',
                    Key: fileName,
                    Body: file,
                },
                (err, data) => {
                    if (err) {
                        console.error(err)
                    } else {
                        setFile()
                    }
                },
            )
    }

    const switchCamera = () => {
        const videoElement = document.getElementById(
            'camera-video',
        ) as HTMLVideoElement
        if (!videoElement) {
            console.error('Video element not found')
            return
        }
        const currentDeviceId = videoElement.srcObject
            ? (videoElement.srcObject as MediaStream)
                  .getVideoTracks()[0]
                  .getSettings().deviceId
            : null

        const nextDevice = devices.find(
            (device) =>
                device.kind === 'videoinput' &&
                device.deviceId !== currentDeviceId,
        )

        if (nextDevice) {
            navigator.mediaDevices
                .getUserMedia({
                    video: { deviceId: nextDevice.deviceId },
                    audio: false,
                })
                .then((stream) => {
                    videoElement.srcObject = stream
                    videoElement.play()
                })
                .catch((error) => {
                    console.error('Error switching camera:', error)
                })
        } else {
            toast({
                description: 'No other camera found to switch.',
                variant: 'destructive',
            })
        }
    }

    const handleSetFile = (file: any) => {
        getFile(file?.[0].title)
        setFileUpload(false)
        setUploadFileName(file?.[0].title)
    }

    return (
        <>
            <Button
                variant="ghost"
                size="icon"
                iconOnly
                title="Add comment"
                className="group"
                iconLeft={
                    <Camera
                        className={cn(
                            file
                                ? 'text-curious-blue-400 group-hover:text-curious-blue-400/50'
                                : 'text-neutral-400 group-hover:text-neutral-400/50',
                            'will-change-transform will-change-width will-change-padding transform-gpu',
                            'group-hover:transition-colors group-hover:ease-out group-hover:duration-300',
                        )}
                        size={24}
                    />
                }
                onClick={() => handleOpenCameraDialog(false)}
            />
            <AlertDialogNew
                openDialog={openCameraDialog}
                setOpenDialog={setOpenCameraDialog}
                title={fileUpload ? 'Files' : 'Camera'}
                handleCreate={() => {
                    if (image) {
                        uploadFile(image)
                        setOpenCameraDialog(false)
                    } else {
                        toast({
                            description: 'Please capture an image first.',
                            variant: 'destructive',
                        })
                    }
                }}
                handleCancel={() => {
                    setOpenCameraDialog(false)
                    setImage(null)
                    setDisplayImage(false)
                    turnOffCamera()
                }}
                actionText="Save"
                cancelText="Close"
                loading={false}>
                <div className="flex flex-col items-center">
                    {fileUpload ? (
                        <UploadCloudFlare
                            // files={[{ title: file }]}
                            setFiles={handleSetFile}
                            accept="image/*"
                            bucketName="captures"
                            multipleUpload={false}
                            prefix={logentryID + '-'}
                        />
                    ) : (
                        <>
                            <video
                                id="camera-video"
                                style={{
                                    display: displayImage ? 'none' : 'block',
                                }}></video>
                            <img
                                src={image}
                                alt="Captured"
                                style={{
                                    display: displayImage ? 'block' : 'none',
                                }}
                            />
                        </>
                    )}
                </div>
                <div className="flex items-center mt-4 gap-2 justify-between">
                    {!displayImage && !fileUpload && (
                        <Button onClick={captureImage} className="mt-2">
                            Capture
                        </Button>
                    )}
                    {displayImage && !fileUpload && (
                        <Button
                            onClick={() => {
                                setImage(null)
                                setDisplayImage(false)
                                handleOpenCameraDialog(true)
                            }}
                            className="mt-2">
                            Recapture
                        </Button>
                    )}
                    {devices.length > 1 && (
                        <Button
                            onClick={() => {
                                switchCamera()
                            }}
                            className="mt-2">
                            Switch Camera
                        </Button>
                    )}
                    {fileUpload ? (
                        <Button
                            onClick={() => {
                                setFileUpload(false)
                                handleOpenCameraDialog()
                            }}
                            className="mt-2">
                            Capture Image
                        </Button>
                    ) : (
                        <Button
                            onClick={() => {
                                turnOffCamera()
                                setFileUpload(true)
                            }}
                            className="mt-2">
                            Upload Image
                        </Button>
                    )}
                </div>
            </AlertDialogNew>
        </>
    )
}
