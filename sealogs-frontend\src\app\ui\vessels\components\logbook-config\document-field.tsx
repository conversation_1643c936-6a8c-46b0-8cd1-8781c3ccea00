'use client'

import FileUpload from '@/components/file-upload'
import { Button } from '@/components/ui'
import { XCircleIcon } from 'lucide-react'
import React from 'react'

interface IComponentProps {
    fileUploadText: string
    documents: Array<Record<string, any>>
    setDocuments: Function
    showFileUpload?: boolean
}

export function DocumentField({
    fileUploadText,
    documents,
    setDocuments,
    showFileUpload = true,
}: IComponentProps) {
    return (
        <>
            {documents.length > 0 &&
                documents.map((doc: any, index: number) => (
                    <DocumentItem
                        title={doc.title}
                        key={doc.id + '_' + index}
                        onClick={() => {
                            setDocuments(
                                documents.filter(
                                    (document: any) => document.id !== doc.id,
                                ),
                            )
                        }}
                    />
                ))}
            {showFileUpload && (
                <FileUpload
                    setDocuments={(files: any[] | ((prev: any[]) => any[])) =>
                        setDocuments(files)
                    }
                    text={fileUploadText}
                    documents={documents}
                />
            )}
        </>
    )
}

function DocumentItem({
    title,
    onClick,
}: {
    title: string
    onClick: Function
}) {
    return (
        <div>
            <div className="flex items-center w-full justify-between mb-2 uppercase">
                <span>{title}</span>
                <Button
                    onClick={() => onClick()}
                    size={'icon'}
                    variant={'destructive'}>
                    <XCircleIcon />
                </Button>
            </div>
        </div>
    )
}
