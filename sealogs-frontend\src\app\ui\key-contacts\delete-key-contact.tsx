'use client'

import { DELETE_KEY_CONTACTS } from '@/app/lib/graphQL/mutation'
import { AlertDialogNew, Button, H2, H3 } from '@/components/ui'
import { toast } from '@/hooks/use-toast'
import { useMutation } from '@apollo/client'
import { Trash2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

export default function DeleteKeyContact({
    id,
    fullName,
}: {
    id: number
    fullName: string
}) {
    const router = useRouter()
    const [openConfirm, setOpenConfirm] = useState(false)

    const [mutationDelete] = useMutation(DELETE_KEY_CONTACTS, {
        onCompleted: (response: any) => {
            if (
                response.deleteKeyContacts &&
                response.deleteKeyContacts.length > 0
            ) {
                router.push('/key-contacts')
            } else {
                toast({
                    description: 'Error deleting key contact',
                    variant: 'destructive',
                })
            }
        },
        onError: (error: any) => {
            console.error('mutationDeleteKeyContacts error:', error.message)
        },
    })

    const handleDelete = () => {
        mutationDelete({
            variables: {
                ids: [id],
            },
        })
    }

    return (
        <>
            <Button
                iconLeft={Trash2}
                variant="destructive"
                color="rose"
                onClick={() => setOpenConfirm(true)}>
                Delete
            </Button>
            <AlertDialogNew
                openDialog={openConfirm}
                setOpenDialog={setOpenConfirm}
                handleCreate={handleDelete}
                variant="danger"
                actionText="Delete Key Contact">
                <H3 className="text-xl">Delete Key Contact</H3>
                <div className="my-4 flex items-center">
                    Are you sure you want to delete {fullName}?
                </div>
            </AlertDialogNew>
        </>
    )
}
