'use client'

import React from 'react'
import { <PERSON>, Sun, <PERSON> } from 'lucide-react'
import {
    DropdownMenuItem,
    DropdownMenuPortal,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu'
import { useTheme } from 'next-themes'

const SidebarTheme = () => {
    const { theme, setTheme } = useTheme()

    const getTheme = () => {
        switch (theme) {
            case 'dark':
                return 'Dark'
            case 'light':
                return 'Light'
            default:
                return 'System'
        }
    }

    return (
        <DropdownMenuSub>
            <DropdownMenuSubTrigger>
                <div className="flex items-center gap-2.5">
                    {getTheme()} Theme
                </div>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
                <DropdownMenuSubContent>
                    <DropdownMenuItem onClick={() => setTheme('light')}>
                        Light
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setTheme('dark')}>
                        Dark
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setTheme('system')}>
                        System
                    </DropdownMenuItem>
                </DropdownMenuSubContent>
            </DropdownMenuPortal>
        </DropdownMenuSub>
    )
}

export default SidebarTheme
